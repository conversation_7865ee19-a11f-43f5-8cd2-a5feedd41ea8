/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import type { DownloadOptions, PreFileResponse } from '@/@types/download';
import type { DeviceHandler } from '../components/DeviceHandler';
import type { FileDownloader } from '../components/FileDownloader';
import type { ProgressTracker } from '../components/ProgressTracker';
import { apiPost } from '@/server/services/api';

export class DownloadService {
  constructor(
    private readonly fileDownloader: FileDownloader,
    private readonly deviceHandler: Devi<PERSON>Handler,
    private readonly progressTracker: ProgressTracker
  ) {}

  private isS3OrSiengeUrl(url: string): boolean {
    const allowedDomains = [
      'https://s3clienteappcury.s3.amazonaws.com',
      'https://curyhomolog.sienge.com.br',
      'https://curyempreendimentos.sienge.com.br'
    ];
    return allowedDomains.some((domain) => url.startsWith(domain));
  }

  private async prefetchFile(options: DownloadOptions): Promise<PreFileResponse | null> {
    try {
      // Atualiza progresso para 20% - Verificando documento
      this.progressTracker.updateProgress(20);

      const response = await apiPost<PreFileResponse>('checkDocumentToDownload', {
        src: options.src,
        filename: options.name,
        objId: options.objId
      });

      // Atualiza progresso para 40% - Documento verificado
      this.progressTracker.updateProgress(40);

      return response;
    } catch (error) {
      this.progressTracker.stop();
      throw error;
    }
  }

  async download(options: DownloadOptions): Promise<void> {
    let finalFile: Blob | string | undefined = options.src;
    let finalName = options.name;

    // Inicia o progresso - 0%
    this.progressTracker.start();

    try {
      if (!this.isS3OrSiengeUrl(options.src) || options.src === '') {
        try {
          // Progresso será atualizado dentro do prefetchFile (20% - 40%)
          const response = await this.prefetchFile(options);
          finalFile = response?.file as string;
          finalName = response?.filename as string;
        } catch (error) {
          this.progressTracker.stop();
          console.error('Download error:', error);
          throw error;
        }
      } else {
        // Para URLs S3/Sienge, atualiza progresso para 30%
        this.progressTracker.updateProgress(30);

        const nameChuck = options.name?.split('.') || [];
        const extChuck = finalFile?.split('.') || [];
        if (typeof nameChuck === 'string' || nameChuck.length === 1) {
          finalName += `.${extChuck[extChuck.length - 1]}`;
        }
      }

      // Progresso 50% - Iniciando download do arquivo
      this.progressTracker.updateProgress(50);

      // O FileDownloader.downloadBlob já atualiza o progresso de 50% a 90%
      if (finalFile === '') {
        this.progressTracker.updateProgress(60);
        const newFile = await this.prefetchFile(options);
        finalFile = newFile?.file as string;
        finalFile = await this.fileDownloader.downloadBlob(finalFile, finalName);
      }
      finalFile = await this.fileDownloader.downloadBlob(finalFile as string, finalName);

      // Verifica se o arquivo é muito pequeno (possível erro)
      if (finalFile.size < 400) {
        this.progressTracker.updateProgress(60);
        const newFile = await this.prefetchFile(options);
        finalFile = newFile?.file as string;
        finalFile = await this.fileDownloader.downloadBlob(finalFile, finalName);
      }

      // Progresso 95% - Preparando para salvar
      this.progressTracker.updateProgress(100);

      await Promise.resolve(this.deviceHandler.triggerDownload(finalFile, finalName));

      // Progresso 100% - Download concluído (será chamado automaticamente pelo ProgressTracker)
      this.progressTracker.stop();
    } catch (error) {
      this.progressTracker.stop();
      console.error('Download error:', error);
      throw error;
    }
  }
}
