/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { BrowserUtils } from '../BrowserUtils';

export interface DeviceHandler {
  triggerDownload(file: Blob | string, filename: string): void;
}

export class MobileDeviceHandler implements DeviceHandler {
  triggerDownload(path: string, filename: string): void {
    BrowserUtils.ActionReactNative({
      type: 'downloadFromAPI',
      data: { path, namefile: filename }
    });
  }
}

export class DesktopDeviceHandler implements DeviceHandler {
  triggerDownload(blob: Blob, filename: string): void {
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
  }
}
