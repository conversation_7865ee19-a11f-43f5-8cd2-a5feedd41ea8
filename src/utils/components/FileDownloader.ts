/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import type { ProgressTracker } from './ProgressTracker';

export class FileDownloader {
  constructor(private readonly progressTracker: ProgressTracker) {}

  async downloadBlob(url: string, filename: string): Promise<Blob> {
    const response = await fetch(
      `/services/download?url=${encodeURIComponent(url)}&filename=${encodeURIComponent(filename)}`
    );

    if (!response.ok) throw new Error('Failed to download file');

    const totalBytes = Number.parseInt(response.headers.get('Content-Length') || '0', 10);
    const reader = response.body?.getReader();
    if (!reader) throw new Error('Unable to read response');

    const chunks: Uint8Array[] = [];
    let loadedBytes = 0;
    let loading = true;
    
    while (loading) {
      const { done, value } = await reader.read();
      if (done) {
        loading = false;
        break;
      }

      chunks.push(value);
      loadedBytes += value.length;
      
      // Progresso do download do arquivo entre 50% e 90%
      const downloadProgress = totalBytes > 0 
        ? Math.round((loadedBytes / totalBytes) * 100) 
        : 0;
      
      // Mapeia o progresso de download (0-100%) para o intervalo 50-90%
      const mappedProgress = 50 + (downloadProgress * 0.4);
      
      this.progressTracker.updateProgress(Math.min(90, mappedProgress));
    }

    return new Blob(chunks);
  }
}
