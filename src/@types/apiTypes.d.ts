/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

export type BodyType = Record<string, string | number | boolean | object | null | undefined> | null;
/**
 * Tipos de métodos HTTP suportados
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

export interface ApiOptions {
  method: HttpMethod;
  route: string;
  params?: { queryString?: Record<string, string> };
  body?: BodyType;
  file?: File | null;
  isNegotiate?: boolean;
  cache?: 'no-store' | 'force-cache';
}

export interface ErrorType {
  default?: string;
  errors: {
    default?: string;
  };
}
export interface ErrorNegotiateType {
  code: number;
  message: string;
}

/**
 * Interface para opções de requisição à API
 */
export interface ApiRequestOptions {
  method: HttpMethod;
  path: string;
  body?: any;
  queryParams?: Record<string, string | number>;
  requiresAuth?: boolean;
  revalidatePaths?: string[];
  cache?: 'no-store' | 'force-cache';
  shouldRedirectOnAuthError?: boolean;
  revalidateTime?: number;
  file?: File | null;
}
