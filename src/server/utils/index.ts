'use server';
import { API_URL } from '@/constants';
import { cookies } from 'next/headers';
import { getServerToken } from '../services/cookiesService';

/**
 * Constrói URL com parâmetros de consulta
 */
export async function buildApiUrl(
  path: string,
  queryParams?: Record<string, string | number | boolean>
): Promise<string> {
  const baseUrl = path.startsWith('http') ? '' : API_URL;
  const url = new URL(`${baseUrl}/${path}`);
  if (queryParams) {
    Object.entries(queryParams).forEach(([key, value]) => {
      url.searchParams.append(key, String(value));
    });
  }
  return url.toString();
}

/**
 * Obtém cabeçalhos para requisição
 */
export async function getApiHeaders(requiresAuth: boolean = true): Promise<HeadersInit> {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };

  if (requiresAuth) {
    const token = await getServerToken();
    
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Adicionar token de impersonificação se existir
    const cookieStore = await cookies();
    const impersonateToken = cookieStore.get('impersonate_token')?.value;
    if (impersonateToken) {
      headers['X-Impersonation-Token'] = impersonateToken;
    }
  }

  return headers;
}
