/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use server';

import { cookies } from 'next/headers';
import { apiPost } from './api';

/**
 * Limpa cookies de autenticação (usado no logout)
 */
export async function getAuthCookies(): Promise<string[]> {
  const cookieStore = await cookies();
  const cookiesAuth = [
    'token',
    'AccountId',
    'user.temp-cpf',
    'user.temp-cnpj',
    'user.temp-email',
    'user.temp-userLogin',
    'user.temp-deviceinfo',
    'contentSelected',
    'device_info',
    'auth/impersonate'
  ];
  const cookiesAuthValues: string[] = [];
  await Promise.all(
    cookiesAuth.map((name) => {
      const cookie = cookieStore.get(name);
      if (cookie) {
        cookiesAuthValues.push(cookie.value);
      }
    })
  );

  return cookiesAuthValues;
}

export async function getCookies(): Promise<Record<string, string>> {
  const cookieStore = await cookies();

  // Obtém todos os cookies da sessão atual
  const allCookies = cookieStore.getAll();

  // Cria um objeto para armazenar todos os cookies
  const allCookiesValues: Record<string, string> = {};

  // Itera sobre todos os cookies e os adiciona ao objeto
  allCookies.forEach((cookie) => {
    allCookiesValues[cookie.name] = cookie.value;
  });

  return allCookiesValues;
}

/**
 * Limpa cookies de autenticação (usado no logout)
 */
export async function clearAuthCookies(deleteServerCookies: boolean = true): Promise<void> {
  const cookieStore = await cookies();
  const cookiesToClear = [
    'token',
    'AccountId',
    'device_info',
    'user.temp-cpf',
    'user.temp-cnpj',
    'user.temp-email',
    'user.temp-userLogin',
    'contentSelected'
  ];
  if (deleteServerCookies) {
    const device_info = cookieStore.get('device_info')?.value;
    apiPost('b2/auth/logout', {
      device_info: device_info
    });
  }

  await Promise.all(
    cookiesToClear.map((name) => {
      const cookie = cookieStore.get(name);
      if (cookie) {
        cookieStore.delete(name);
      }
      return Promise.resolve();
    })
  );
}

/**
 * Define cookies de autenticação após login bem-sucedido
 */
export async function setAuthCookies(
  token: string,
  expiresAt: string,
  accountId: string,
  device_info: string,
  userData: Record<string, string>,
  rememberMe: boolean = false
): Promise<void> {
  const cookieStore = await cookies();

  // Define opções de cookie baseado em lembrar-me
  const maxAge = rememberMe ? 60 * 60 * 24 * 30 : 60 * 60 * 24; // 30 dias ou 1 dia

  const options = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge,
    path: '/'
  };

  // Define cookies principais
  cookieStore.set('token', token, options);
  cookieStore.set('expires_at', expiresAt, options);
  cookieStore.set('AccountId', accountId, options);
  cookieStore.set('device_info', device_info, options);

  // Define cookies de usuário temporários
  Object.entries(userData).forEach(([key, value]) => {
    if (value) {
      cookieStore.set(`user.temp-${key.toLowerCase()}`, value, options);
    }
  });
}

/**
 * Obtém o token de autenticação dos cookies
 */
export async function getServerToken(): Promise<string | null> {
  const cookieStore = await cookies();
  return cookieStore.get('token')?.value || null;
}

/**
 * Obtém o ID da conta do usuário dos cookies
 */
export async function getServerAccountId(): Promise<string | null> {
  const cookieStore = await cookies();
  return cookieStore.get('AccountId')?.value || null;
}
