/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use server';

import { ApiRequestOptions } from '@/@types/apiTypes';
import { buildFormData } from '@/hooks/apiUtils';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { buildApiUrl, getApiHeaders } from '../utils';
import { clearAuthCookies } from './cookiesService';

/**
 * Server Action centralizada para chamadas à API
 * Esta é a função principal para fazer requisições à API do servidor
 */
export async function serverFetch<T = any>(options: ApiRequestOptions): Promise<T> {
  try {
    const {
      method,
      path,
      body,
      queryParams,
      requiresAuth = true,
      revalidatePaths = [],
      cache = 'no-cache',
      shouldRedirectOnAuthError = false,
      revalidateTime = 60,
      file
    } = options;
    const url = await buildApiUrl(path, queryParams);
    let headers = await getApiHeaders(requiresAuth);
    let requestBody: any = JSON.stringify(body);
    if (body && body.file instanceof File) {
      headers = Object.fromEntries(
        Object.entries(headers).filter(([key]) => key.toLowerCase() !== 'content-type')
      );
      requestBody = buildFormData(body, file);
    }
    const response = await fetch(url, {
      method,
      headers,
      body: requestBody,
      // cache: 'no-cache',
      cache: 'force-cache',
      next: { tags: [path], revalidate: revalidateTime }
      // next: { tags: [path], revalidate: 0 }
    });
    
    if (!response.ok) {
      if (response.status === 401) {
        //
        if (shouldRedirectOnAuthError) {
          await clearAuthCookies();
          redirect('/login');
        } else {
          console.log('Erro de autenticação');
          // throw new Error('AUTH_ERROR');
        }
      }

      const errorData = await response.json().catch(() => ({ message: 'Erro desconhecido' }));
      return errorData.message || `Erro ${response.status} na requisição à API`;
    }

    if (response.status === 204 || response.headers.get('content-length') === '0') {
      return null as T;
    }

    if (revalidatePaths.length > 0) {
      if (revalidatePaths.length > 0) {
        try {
          revalidatePaths.forEach((path) => revalidatePath(path));
        } catch (revalidateError) {
          console.error('Erro ao revalidar caminhos:', revalidateError);
        }
      }
    }

    const data = (await response.json()) as T;
    return data;
  } catch (error) {
    console.error('Erro na requisição ao servidor:', error);
    throw error;
  }
}

/**
 * Função GET para API
 */
export async function apiGet<T = any>(
  path: string,
  queryParams?: Record<string, string | number>,
  file?: File | null,
  requiresAuth: boolean = true,
  revalidatePaths: string[] = [],
  shouldRedirectOnAuthError: boolean = false
): Promise<T> {
  return await serverFetch<T>({
    method: 'GET',
    path,
    queryParams,
    file,
    requiresAuth,
    revalidatePaths,
    shouldRedirectOnAuthError
  });
}

/**
 * Função POST para API
 */
export async function apiPost<T = any>(
  path: string,
  body?: any,
  queryParams?: Record<string, string | number>,
  file?: File | null,
  requiresAuth: boolean = true,
  revalidatePaths: string[] = [],
  shouldRedirectOnAuthError: boolean = false
): Promise<T> {
  return serverFetch<T>({
    method: 'POST',
    path,
    body,
    queryParams,
    file,
    requiresAuth,
    revalidatePaths,
    shouldRedirectOnAuthError
  });
}

/**
 * Função PUT para API
 */
export async function apiPut<T = any>(
  path: string,
  body?: any,
  queryParams?: Record<string, string | number>,
  file?: File | null,
  requiresAuth: boolean = true,
  revalidatePaths: string[] = [],
  shouldRedirectOnAuthError: boolean = false
): Promise<T> {
  return serverFetch<T>({
    method: 'PUT',
    path,
    body,
    queryParams,
    file,
    requiresAuth,
    revalidatePaths,
    shouldRedirectOnAuthError
  });
}

/**
 * Função DELETE para API
 */
export async function apiDelete<T = any>(
  path: string,
  queryParams?: Record<string, string | number>,
  file?: File | null,
  requiresAuth: boolean = true,
  revalidatePaths: string[] = [],
  shouldRedirectOnAuthError: boolean = false
): Promise<T> {
  return serverFetch<T>({
    method: 'DELETE',
    path,
    queryParams,
    file,
    requiresAuth,
    revalidatePaths,
    shouldRedirectOnAuthError
  });
}

/**
 * Função PATCH para API
 */
export async function apiPatch<T = any>(
  path: string,
  body?: any,
  queryParams?: Record<string, string | number>,
  file?: File | null,
  requiresAuth: boolean = true,
  revalidatePaths: string[] = [],
  shouldRedirectOnAuthError: boolean = false
): Promise<T> {
  return serverFetch<T>({
    method: 'PATCH',
    path,
    body,
    queryParams,
    file,
    requiresAuth,
    revalidatePaths,
    shouldRedirectOnAuthError
  });
}
