'use server';

import { CaseData } from '@/@types/cases';
import { apiPost } from '../services/api';

/**
 * Envia um caso para o servidor
 */
export const sendCaseToServer = async (caseData: CaseData): Promise<any> => {
  try {
    // const response = await fetch('/api/case/insertCaseNegociaAntecipa', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json'
    //   },
    //   body: JSON.stringify(caseData)
    // });

    const response = await apiPost('/api/case/insertCaseNegociaAntecipa', JSON.stringify(caseData));

    if (!response.ok) {
      throw new Error('Falha ao enviar o caso');
    }

    return await response.json();
  } catch (error) {
    console.error('Erro ao enviar o caso:', error);
    throw error;
  }
};
