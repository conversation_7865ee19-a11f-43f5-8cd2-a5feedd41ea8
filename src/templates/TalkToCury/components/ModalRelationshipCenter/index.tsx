/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */
'use client';
import Image from 'next/image';
import React from 'react';

import { useAppContext } from '@/contexts/AppContext';
import { AnimatePresence, motion } from 'framer-motion';
import MenuModal from './MenuModal';

const ModalRelationshipCenter: React.FC = () => {
  const { isModalVisible, setIsModalVisible } = useAppContext();
  if (isModalVisible !== true) return null;
  return (
    <>
      {isModalVisible && (
        <AnimatePresence mode='wait'>
          <motion.div
            key='loading'
            initial={{ opacity: isModalVisible ? 0 : 1 }}
            animate={{ opacity: isModalVisible ? 1 : 0 }}
            exit={{ opacity: isModalVisible ? 0 : 1 }}
            transition={{
              duration: 0.4,
              ease: 'easeInOut'
            }}
            className='absolute flex justify-center items-center w-full h-full bg-[#1A374D99] z-[500000]'>
            <div
              style={{
                width: '80%',
                height: '50%',
                backgroundColor: 'white',
                padding: '20px',
                zIndex: 1000,
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                borderRadius: '15px',
                border: '2px solid rgb(98 130 154)',
                display: 'flex',
                alignItems: 'center'
              }}>
              <div className='relative text-center w-full text-navy-blue flex flex-col justify-around items-center h-[80%]'>
                <Image
                  src='/images/central.png'
                  width={38}
                  height={53}
                  alt='Central de Atendimento'
                />
                <a href='tel:08003149696'>
                  <MenuModal title={'Central de Relacionamento'} phone={'0800 314 9696'} />
                </a>
                <a href='tel:1131171300'>
                  <MenuModal title={'São Paulo'} phone={'(11) 3117-1300'} />
                </a>
                <a href='tel:2135436887'>
                  <MenuModal title={'Rio de Janeiro'} phone={'(21) 3543-6887'} />
                </a>
                <button
                  type='button'
                  style={{
                    position: 'absolute',
                    top: '-14%',
                    right: '-5px',
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: 'bold'
                  }}
                  onClick={() => setIsModalVisible(false)}>
                  X
                </button>
              </div>
            </div>
          </motion.div>
        </AnimatePresence>
      )}
    </>
  );
};

export default ModalRelationshipCenter;
