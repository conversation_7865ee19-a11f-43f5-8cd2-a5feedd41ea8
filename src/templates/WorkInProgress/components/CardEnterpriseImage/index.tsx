/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { FILES_URL } from '@/constants';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import ImageLoad from '@/templates/components/Commons/ImageLoad';
import { AnimatePresence, motion } from 'framer-motion';

interface CardEnterpriseImageProps {
  src: string;
  onClick: () => void;
}
const CardEnterpriseImage = ({ src, onClick }: CardEnterpriseImageProps) => {
  const srcImage = src ? `${FILES_URL}${src}.webp` : '/images/logo.svg';
  return (
    <AnimatePresence>
      <motion.div
        initial='collapsed'
        animate={'open'}
        variants={AnimateFadeIn}
        exit='exit'
        transition={{ duration: 0.5, ease: 'easeInOut' }}
        className='object-cover  w-full h-auto aspect-square'>
        <button
          type='button'
          className=' cursor-pointer flex justify-center items-center bg-white border-white border-[0.25rem] rounded w-full h-auto aspect-square'
          onClick={onClick}>
          <ImageLoad key={srcImage} imageUrl={srcImage} customClass={'aspect-square'} />
        </button>
      </motion.div>
    </AnimatePresence>
  );
};

export default CardEnterpriseImage;
