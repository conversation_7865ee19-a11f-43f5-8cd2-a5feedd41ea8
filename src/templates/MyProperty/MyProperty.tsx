/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useMediaQuery } from '@/hooks';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import MyPropertyDesk from './MyProperty/MyPropertyDesk';
import MyPropertyMobile from './MyProperty/MyPropertyMobile';
import { useAppContext } from '@/contexts/AppContext';
import { useEffect } from 'react';
import { getServerContent } from '@/server/actions';

export default function MyPropertyScreen() {
  const { isMobile } = useMediaQuery();
  const { canBeRender } = useCanBeRender();
  const { content } = useAppContext();

  useEffect(() => {
    const fetchContent = async () => {
      try {
        await getServerContent({
          ProposalId: content.ProposalId || null,
          ContractId: content.ContractId || null,
          type: content.type,
          AccountId: content.AccountId,
          selectedIndex: content.contentSelected,
          EmpreendimentoId: content.Empreendimento?.EmpreendimentoId,
          page: 'MyProperty'
        });
      } catch (error) {
        console.error('Error fetching content:', error);
      }
    };

    fetchContent();
  }, [content]);

  const conditions = [isMobile === null];
  if (!canBeRender({ conditions })) return null;

  return <>{isMobile ? <MyPropertyMobile /> : <MyPropertyDesk />}</>;
}
