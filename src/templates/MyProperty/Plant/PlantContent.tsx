/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import Image from 'next/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';

import { FILES_URL, WHATS_NUMBER } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { useMediaQuery, useModal } from '@/hooks';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import ModalPlant from '@/templates/components/Plant/ModalPlant';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { DeviceUtils } from '@/utils/DeviceUtils';
import { downloadFile } from '@/utils/DownloadUtils';

import TitlePlant from './TitlePlant';
import { apiPost } from '@/server/services/api';

// Interface para os dados da planta
interface PlantData {
  imgPlanta: string | null;
  imgPlantaToDownload: string | null;
  unidadeAtivoName: string;
  empreendimentoName: string;
  documentId: string;
  isImageValid: boolean;
}

// Função para verificar se a imagem realmente carrega
const validateImageExists = (imageUrl: string): Promise<boolean> => {
  if (!imageUrl) return Promise.resolve(false);

  return new Promise((resolve) => {
    const img = document.createElement('img');

    img.onload = () => {
      resolve(true);
    };

    img.onerror = () => {
      resolve(false);
    };

    // Timeout de 10 segundos para evitar travamento
    const timer = setTimeout(() => {
      clearTimeout(timer);
      resolve(false);
    }, 10000);

    img.src = imageUrl;
  });
};

// Componente para exibir a imagem da planta com o botão de download
const PlantImage: React.FC<{
  imgPlanta: string;
  imgPlantaToDownload: string;
  isMobile: boolean | null;
  toggleImage: () => void;
  handleDownload: () => Promise<boolean | null>;
}> = ({ imgPlanta, isMobile, toggleImage, handleDownload }) => (
  <>
    <button type='button' className='flex items-center justify-center mb-4' onClick={toggleImage}>
      <Image
        src={imgPlanta}
        className={isMobile ? 'flex w-full h-full' : 'w-[400px]'}
        width={500}
        height={500}
        priority
        alt='planta'
      />
    </button>
    <div className='w-1/2 ml-1 flex justify-center z-10'>
      <Buttonblue
        text='Fazer Download'
        background='navy-blue'
        color='white'
        disabled={false}
        onClick={handleDownload}
      />
    </div>
  </>
);

// Componente para quando a planta não está disponível
const PlantNotAvailable: React.FC = () => (
  <>
    <p className='text-navy-blue text-lg text-center font-bold tracking-1 leading-5 mt-8'>
      A planta não está disponível no momento.
    </p>
    <p className='text-navy-blue text-lg text-center tracking-1 leading-5 mt-4'>
      Para solicitar uma cópia, entre em contato conosco pelo <WhatsAppLink />
    </p>
    <p className='text-navy-blue text-lg text-center mb-4 tracking-1 leading-5 mt-1'>
      {' '}
      Estaremos prontos para ajudar você!
    </p>
  </>
);

// Componente do link do WhatsApp
const WhatsAppLink: React.FC = () => {
  if (DeviceUtils.isMobileApp()) {
    return (
      <button
        type='button'
        className='text-sm underline text-navy-blue tracking-1 mb-4'
        onClick={() =>
          BrowserUtils.ActionReactNative({
            type: 'openUrlBrowser',
            url: WHATS_NUMBER,
            data: {}
          })
        }>
        WhatsApp. Clique aqui.
      </button>
    );
  }

  return (
    <Link
      className='text-navy-blue text-lg tracking-8 leading-8 underline mb-4'
      href={WHATS_NUMBER}
      target='_blank'>
      WhatsApp. Clique aqui.
    </Link>
  );
};

// Hook para processar os dados da planta
async function usePlantData(content: any, contentSelected: any): Promise<PlantData | null> {
  if (content === null || contentSelected === undefined) {
    return {
      imgPlanta: null,
      imgPlantaToDownload: null,
      unidadeAtivoName: '',
      empreendimentoName: '',
      documentId: '',
      isImageValid: false
    };
  }
  if (content.ProposalId !== null) {
    return {
      imgPlanta: null,
      imgPlantaToDownload: null,
      unidadeAtivoName: content?.UnidadeAtivoName ?? '',
      empreendimentoName: content?.Empreendimento?.name ?? '',
      documentId: '',
      isImageValid: false
    };
  }

  // Faz o parse se for string, senão usa como está
  let plantaArray: any[] = [];

  if (typeof content.planta === 'string') {
    try {
      plantaArray = JSON.parse(content.planta);
    } catch (e) {
      console.error('Erro ao fazer parse de content.planta:', e);
      plantaArray = [];
    }
  } else if (Array.isArray(content.planta)) {
    plantaArray = content.planta;
  }

  let plantaImage = plantaArray?.[0]?.Link__c;
  let documentId = plantaArray?.[0]?.Id;

  if (plantaImage === null || plantaImage === undefined) {
    const response = await apiPost('proposal/updateplanta', {
      ProposalId: content.ProposalId,
      PlantId: content.PlantaId ?? null
    });
    plantaImage = response.data.plantaSrc;
    documentId = response.data.documentId;
  }

  const srcplanta = plantaImage
    ? plantaImage?.startsWith('http')
      ? plantaImage
      : `${FILES_URL}${plantaImage}`
    : null;

  // Remove .webp se existir na srcplanta
  const srcplantaClean = srcplanta ? srcplanta.replace(/\.webp$/, '') : null;

  const validExt = srcplantaClean && !srcplantaClean.endsWith('.webp');
  const validExtPdf = srcplantaClean && !srcplantaClean.endsWith('.pdf');
  const hasExt = validExt ? '.webp' : '';
  const hasExtPdf = validExtPdf ? '.pdf' : '';
  const imgPlanta = srcplantaClean ? `${srcplantaClean}${hasExt}` : null;
  const imgPlantaToDownload = imgPlanta;
  const isImageValid: boolean = imgPlanta ? await validateImageExists(imgPlanta) : false;

  return {
    imgPlanta,
    imgPlantaToDownload,
    unidadeAtivoName: content?.UnidadeAtivoName ?? '',
    empreendimentoName: content?.Empreendimento?.name ?? '',
    documentId: documentId ?? '',
    isImageValid
  };
}

// Componente principal
export default function PlantContent() {
  const { toggleModal } = useModal();
  const { isMobile } = useMediaQuery();
  const [showImage, setShowImage] = useState(false);
  const [plantData, setPlantData] = useState<PlantData | null>(null);
  const { content, contentSelected } = useAppContext();
  useEffect(() => {
    async function getPlantData() {
      const plantData = await usePlantData(content, contentSelected);
      setPlantData(plantData);
    }
    getPlantData();
  }, [content, contentSelected]);

  // Early return se os dados não estiverem disponíveis
  if (!plantData) return null;

  const {
    imgPlanta,
    imgPlantaToDownload,
    unidadeAtivoName,
    empreendimentoName,
    documentId,
    isImageValid
  } = plantData;

  // Função para fazer download da planta
  const download = async () => {
    if (imgPlantaToDownload === null) return null;
    await downloadFile('', 'planta', toggleModal, documentId);
    return true;
  };

  // Função para alternar a visualização da imagem
  const toggleImage = () => setShowImage(!showImage);

  return (
    <div className='flex flex-col bg-white rounded px-4 items-center justify-start py-4 h-[auto]'>
      <div className='flex flex-col items-center justify-center w-full'>
        {imgPlanta ? (
          <PlantImage
            imgPlanta={imgPlanta}
            imgPlantaToDownload={imgPlantaToDownload || ''}
            isMobile={isMobile}
            toggleImage={toggleImage}
            handleDownload={download}
          />
        ) : (
          <PlantNotAvailable />
        )}
      </div>

      <TitlePlant
        UnidadeAtivoName={unidadeAtivoName}
        EmpreendimentoName={empreendimentoName}
      />

      {imgPlanta && (
        <ModalPlant
          show={showImage}
          toogleImage={toggleImage}
          imgPlanta={imgPlanta}
          titlePlant={
            <TitlePlant
              UnidadeAtivoName={unidadeAtivoName}
              EmpreendimentoName={empreendimentoName}
              CustomClass='text-base text-white tracking-1 leading-6 mt-5 text-center whitespace-pre-wrap'
            />
          }
        />
      )}
    </div>
  );
}
