/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { motion } from 'framer-motion';
import React, { useMemo, useRef } from 'react';
import { Autoplay, Pagination } from 'swiper/modules';
import { Swiper, SwiperSlide } from 'swiper/react';

// import ImageLoad from '@/templates/components/Commons/ImageLoad';

import 'swiper/css';
import 'swiper/css/pagination';

import { FILES_URL } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import SpinnerLoading from '@/templates/components/Commons/Loading/SpinnerLoading';
import { StringUtils } from '@/utils/StringUtils';
import Image from 'next/image';
import './styles.css';

const INITIAL_IMAGE = 'cloudx_cms__SS_Carousel_Slide__c/banner-default';

const ImageSlider = () => {
  const { content, isMobile } = useAppContext();
  const imageLoading = useRef<boolean>(true);
  let animateStart = {};
  // const animateEnd = {};
  let initialOpacity = {};
  let initialTransition = {};
  animateStart = { opacity: 1 };
  initialOpacity = { opacity: 0 };
  initialTransition = { opacity: { duration: 0.4 } };
  const images = StringUtils.ParselStringToArray(
    content?.Empreendimento?.imgsCarrossel,
    INITIAL_IMAGE
  );

  const pagination = useMemo(() => {
    return {
      clickable: true,
      renderBullet(index: number, classNameBullet: string) {
        return (
          '<span style="margin-top:50px; height: 0.5rem; width: 1.75rem; margin-left: 0.25rem; margin-right: 0.25rem; border-radius: 0.25rem; margin-top:50px" class="' +
          classNameBullet +
          '"></span>'
        );
      }
    };
  }, []);

  if (!images) return null;
  return (
    <>
      <div className='flex w-[100%] z-index-[1000000]'>
        <div className='relative w-full mx-auto max-md:px-1 overflow-hidden h-[auto] pb-[30px]'>
          <div className='w-full h-96 relative max-md:h-36'>
            {images && images.length > 0 ? (
              <Swiper
                className='ImageSliderSwiper h-[100%] overflow'
                modules={[Pagination, Autoplay]}
                spaceBetween={5}
                slidesPerView={1}
                loop={images.length > 1}
                pagination={pagination}
                grabCursor={true}
                autoplay={{
                  delay: 4500,
                  disableOnInteraction: false
                }}>
                {images.map((imageUrl: string) => {
                  imageLoading.current = true;
                  const imageLoaded = () => {
                    imageLoading.current = false;
                  };

                  return (
                    <React.Fragment key={`images_carrossel_${imageUrl}`}>
                      {imageUrl ? (
                        <SwiperSlide className='h-[100%]' key={`SwiperSlide_${imageUrl}`}>
                          <>
                            <motion.div
                              key={`Image_${imageUrl}`}
                              initial={initialOpacity}
                              animate={animateStart}
                              transition={initialTransition}
                              onLoad={imageLoaded}
                              className='w-full h-full'>
                              <Image
                                src={`${FILES_URL}${imageUrl}.webp`}
                                style={{ objectFit: 'cover' }}
                                alt={`${imageUrl}`}
                                width={!isMobile ? 1000 : 300}
                                height={!isMobile ? 1000 : 300}
                                quality={75}
                                loading='lazy'
                                className={
                                  'absolute z-[100000] w-full h-full flex flex-col justify-items-start items-center bg-[rgba(255,255,255,0.8)]'
                                }
                              />
                            </motion.div>
                            {imageLoading.current && (
                              <div className='relative flex w-full h-full min-h-[auto] justify-center items-center '>
                                <SpinnerLoading
                                  width={50}
                                  height={50}
                                  className={'absolute w-auto h-auto max-w-16 max-h-16'}
                                />
                              </div>
                            )}
                          </>
                        </SwiperSlide>
                      ) : null}
                    </React.Fragment>
                  );
                })}
              </Swiper>
            ) : null}
          </div>
        </div>
      </div>
    </>
  );
};

export default ImageSlider;
