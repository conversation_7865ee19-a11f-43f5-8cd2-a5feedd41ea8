/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useAppContext } from '@/contexts/AppContext';
import { Show } from '@/utils/components/Show';
import {
  BoletoAtoCard,
  DefaultNoContent,
  ImageSlider,
  MostAccessedMenu,
  Sidebar
} from '@/utils/lazyImports';
import React, { Suspense, useLayoutEffect } from 'react';
import SpinnerLoading from '../components/Commons/Loading/SpinnerLoading';
import SelectContent from '../components/Commons/SelectContent';
import HomeDeskScreen from './HomeDeskScreen';

export default function HomeScreen(): React.ReactElement | null {
  const { content, isMobile, setShowSidebar } = useAppContext();
  useLayoutEffect(() => {
    setShowSidebar(true);
  }, []);

  if (isMobile === null || content === null) {
    return <SpinnerLoading />;
  }

  if (!content) {
    return (
      <Suspense fallback={<SpinnerLoading />}>
        <DefaultNoContent text='Não foram encontradas propostas ou contratos.' />
      </Suspense>
    );
  }
  const showBoletoAto = content
    ? content?.statusBoletoAto !== 'Recebimento' && content?.type !== 'sindico'
    : null;

  return (
    <div className='w-full home'>
      <Show when={isMobile}>
        <Suspense fallback={<SpinnerLoading />}>
          <SelectContent />
        </Suspense>
      </Show>

      <Suspense fallback={<SpinnerLoading />}>
        <ImageSlider />
      </Suspense>

      {showBoletoAto ? (
        <Suspense fallback={<SpinnerLoading />}>
          <BoletoAtoCard />
        </Suspense>
      ) : (
        <>
          <Suspense fallback={<SpinnerLoading />}>
            <MostAccessedMenu />
          </Suspense>
          <Show when={isMobile} fallback={<HomeDeskScreen />}>
            <Suspense fallback={<SpinnerLoading />}>
              <Sidebar />
            </Suspense>
          </Show>
        </>
      )}
    </div>
  );
}
