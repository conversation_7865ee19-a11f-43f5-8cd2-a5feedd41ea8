 
/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import {
  CategoriasVideosResponse,
  CategoriaVideo,
  OptionsCategoriasTempProps
} from '@/@types/videos';
import { useAppContext } from '@/contexts/AppContext';
import { useApi } from '@/hooks/useApi';
import { useEffect, useState } from 'react';

export const useVideos = () => {
  const [error, setError] = useState<Error | null>(null);
  const [categorias, setCategorias] = useState<CategoriaVideo[]>([]);
  const [videosFiltrados, setVideosFiltrados] = useState<CategoriaVideo[]>([]);
  const [optionsCategorias, setOptionsCategorias] = useState<OptionsCategoriasTempProps[]>([]);

  const { content } = useAppContext();

  const { getFetch } = useApi();

  useEffect(() => {
    const loadCategorias = async () => {
      try {
        if (!content) {
          throw new Error('Content is null');
        }
        const response = await getFetch<CategoriasVideosResponse>({
          method: 'GET',
          route: 'showCategorias',
          params: {
            queryString: { userSindico: content.type }
          }
        });

        if (response?.data) {
          setCategorias(response.data.categorias);
          const optionsCategoriasTemp: OptionsCategoriasTempProps[] = [
            {
              id: 0,
              value: 'todos',
              name: 'Todos'
            }
          ];
          response.data.categorias.forEach((item) => {
            optionsCategoriasTemp.push({
              id: item.id,
              value: item.CategoriaId,
              name: item.Name
            });
          });
          setOptionsCategorias(optionsCategoriasTemp);
          setVideosFiltrados(response.data.categorias);
        } else {
          throw new Error('No data returned');
        }
      } catch (err: unknown) {
        console.error('Error loading categorias:', err); // Log de erro
        setError(err as Error);
      }
    };

    if (content && content.type) {
      loadCategorias();
    } else {
      setError(new Error('Content type is null or undefined'));
    }
  }, [content?.type]);

  const filterVideoCategoria = (value: string | number) => {
    if (value === 'todos') {
      setVideosFiltrados(categorias);
    } else {
      const videosFiltradosTemp = categorias.filter((categoria) => categoria.CategoriaId === value);
      setVideosFiltrados(videosFiltradosTemp);
    }
  };

  return {
    error,
    filterVideoCategoria,
    videosFiltrados,
    optionsCategorias
  };
};
