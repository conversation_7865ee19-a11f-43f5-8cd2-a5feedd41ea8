/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { motion } from 'framer-motion';

interface AndamentoProps {
  porcentagem: string;
  nome: string;
}
const Andamento = ({ porcentagem = '0', nome }: AndamentoProps) => {
  const porc = porcentagem ?? 0;
  return (
    <>
      <hr className='hidden border-t-1 border-white mb-0 mt-0 w-full max-md:block' />
      <div className='flex flex-row justify-between items-center py-3 border-b-1 border-light-gray last:border-b-0'>
        <p className='tracking-widest text-xs font-normal text-navy-blue w-24'>{nome}</p>
        <div className='flex flex-row items-center w-7/12'>
          <div className='flex align-items-start justify-start bg-light-gray w-9/12 rounded h-5 relative mr-3 max-md:bg-white p-[2px] max-md:h-6 border-1 border-solid border-[#fff]'>
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: porc + '%' }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className='bg-blue-secondary h-[18px] rounded'
            />
          </div>
          <p className='tracking-widest font-normal text-xs text-navy-blue'>{porc}%</p>
        </div>
      </div>
    </>
  );
};

export default Andamento;
