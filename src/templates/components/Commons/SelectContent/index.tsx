/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

import { Animation } from '@/@types/animations';
import { Content } from '@/@types/content';
import { FILES_URL } from '@/constants';
import { AnimateSelectContent } from '@/constants/ANIMATIONS';
import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { MiscUtils } from '@/utils/MiscUtils';
import { AnimatePresence, motion } from 'framer-motion';
import ButtonContentHome from './ButtonContentHome';
import ContentHome from './ContentHome';

/**
 * Componente para seleção de conteúdo (empreendimento)
 * Funciona como um dropdown de seleção para os conteúdos do usuário
 */
export default function SelectContent() {
  const {
    contents,
    content,
    contentSelected,
    selectContent,
    loadMoreContents,
    hasMoreContent,
    isLoadingMore
  } = useAppContext();
  const [showItems, setShowItems] = useState(false);
  const { setLoadingInfo } = useLoading();
  const containerRef = useRef<HTMLDivElement>(null);
  const toggleItems = useCallback(() => {
    if (contents && contents.length > 1) {
      setShowItems((prev) => !prev);
    }
  }, [contents]);

  // Seleciona um conteúdo
  const handleSelectContent = useCallback(
    async (index: number) => {
      setShowItems(false);
      setLoadingInfo(true, 'Carregando conteúdo...');
      await selectContent(index);
      setLoadingInfo(false);
    },
    [selectContent, setLoadingInfo]
  );

  // Obtém o texto para a unidade atual
  const textUnidade = useCallback(() => {
    if (!content) return '';
    return MiscUtils.defineTextUnidade(content.type, content.UnidadeAtivoName);
  }, [content]);

  // Handle scroll event to load more content
  const handleScroll = useCallback(() => {
    if (!containerRef.current || !showItems || isLoadingMore || !hasMoreContent) return;

    const container = containerRef.current;
    const { scrollTop, scrollHeight, clientHeight } = container;

    // If we're near the bottom (within 50px), load more content
    if (scrollHeight - scrollTop - clientHeight < 50) {
      loadMoreContents();
    }
  }, [showItems, isLoadingMore, hasMoreContent, loadMoreContents]);

  // Add scroll event listener
  useEffect(() => {
    const container = containerRef.current;
    if (container && showItems) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [showItems, handleScroll]);

  const variants: Animation = [];
  // Verifica se temos dados para exibir
  if (!contents || contents.length === 0 || !content) return null;

  // URL da imagem do empreendimento atual
  const imgPath = content?.Empreendimento?.LogoEmpreendimento__c;
  const srcImage = imgPath ? `${FILES_URL}${imgPath}.webp` : '/images/logo.svg';

  return (
    <>
      <div className='relative z-[300] max-w-[410px]'>
        <div className={'z-[300] bg-[#fff] px-[10px]'}>
          <ButtonContentHome
            index={-1}
            name={content?.Empreendimento?.name}
            UnidadeAtivoName={textUnidade()}
            src={srcImage}
            action={toggleItems}
            contents={contents}
            contentSelected={-1}
            showItens={showItems}
          />
        </div>

        {contents && (
          <div
            ref={containerRef}
            id='select-content-container'
            className='flex flex-col h-auto absolute z-[-1] w-full max-h-[400px] overflow-hidden drop-shadow-2xl'>
            <AnimatePresence mode='sync'>
              {showItems &&
                contents.length > 1 &&
                contents?.map((contentMap: Content, index: number) => {
                  // Usar um ID único e estável para cada item
                  const itemId = contentMap.ProposalId || contentMap.ContractId || index;
                  const itemVariants = AnimateSelectContent(index);

                  return (
                    <motion.div
                      key={itemId}
                      initial='collapsed'
                      animate='open'
                      exit='exit'
                      variants={itemVariants}
                      layoutId={`content-item-${itemId}`}
                      onAnimationComplete={() => {
                        const container = document.getElementById('select-content-container');
                        if (container) {
                          if (showItems) {
                            if (container.scrollHeight < 400) {
                              container.classList.add('overflow-hidden');
                            }
                            container.classList.remove('overflow-hidden');
                            container.classList.add('overflow-auto');
                          } else {
                            container.classList.remove('overflow-auto');
                          }
                        }
                      }}>
                      <ContentHome
                        src={contentMap.Empreendimento?.LogoEmpreendimento__c}
                        index={index}
                        showItens={showItems}
                        variants={variants[index]}
                        contentSelected={contentSelected}
                        selectItensInner={() => handleSelectContent(index)}
                        name={contentMap.Empreendimento?.name}
                        UnidadeAtivoName={contentMap.UnidadeAtivoName}
                      />
                    </motion.div>
                  );
                })}
              {showItems && isLoadingMore && (
                <div className='flex justify-center items-center py-3 bg-white w-full h-[400px]'>
                  <div className='animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500'></div>
                </div>
              )}
            </AnimatePresence>
          </div>
        )}
      </div>
    </>
  );
}
