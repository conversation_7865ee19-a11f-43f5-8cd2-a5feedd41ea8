/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { motion } from 'framer-motion';
import React, { useEffect } from 'react';

import { NegotiatedConditionsProps } from '@/@types/installments';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { useConditionsSelection } from '@/hooks/Negotiation/useConditionsSelection';
import { useLoading } from '@/hooks/useLoading';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import { InnerTextCondicions, TextCondicions, TitleCondicions } from '../../mock/TextsCondicions';
import CardNegotiatedConditions from './CardNegotiatedConditions';
import { CardNegotiatedConditionsStatic } from './CardNegotiatedConditionsStatic';

/**
 * Componente para exibir e selecionar condições de negociação ou antecipação
 */
const Conditions: React.FC<NegotiatedConditionsProps> = React.memo(
  ({ negotiatedConditions, paid, selectCondictionBase, confirmAction, cancelAction, show }) => {
    const { setLoadingInfo } = useLoading();
    const {
      condictionSelect,
      indexSelect,
      textButton,
      selectCondiction,
      clearSelection,
      shouldShowStaticCard,
      tryAutoSelect
    } = useConditionsSelection({
      paid,
      negotiatedConditions,
      selectCondictionBase
    });

    /**
     * Efeito para inicialização e limpeza de dados
     */
    useEffect(() => {
      if (show && negotiatedConditions === null) {
        setLoadingInfo(true, 'Carregando as condições negociadas.');
      } else {
        setLoadingInfo(false);
      }

      if (show && negotiatedConditions && negotiatedConditions.length > 0) {
        const success = tryAutoSelect();
      }

      return () => {
        clearSelection();
      };
    }, [show]);

    if (!show) return null;

    if (negotiatedConditions === null) {
      return (
        <motion.div
          initial='collapsed'
          animate='open'
          variants={AnimateFadeIn}
          exit='exit'
          transition={{ duration: 0.5, ease: 'linear' }}
          className='mb-10 flex flex-col items-center justify-center p-4'>
          <p className='text-lg text-navy-blue font-medium'>Carregando condições negociadas...</p>
        </motion.div>
      );
    }

    return (
      <motion.div
        initial='collapsed'
        animate='open'
        variants={AnimateFadeIn}
        exit='exit'
        transition={{ duration: 0.5, ease: 'linear' }}
        className='mb-10'>
        {/* Cabeçalho da seção */}
        {!paid ? (
          <TextCondicions>
            <TitleCondicions>Negocie suas parcelas</TitleCondicions>
            <InnerTextCondicions>Renegocie parcelas em atraso.</InnerTextCondicions>
          </TextCondicions>
        ) : (
          <TextCondicions>
            <TitleCondicions>Antecipe suas parcelas</TitleCondicions>
            <InnerTextCondicions>
              Cliente Cury, disponibilizamos campanhas com descontos especiais para você realizar
              antecipação de suas parcelas.
            </InnerTextCondicions>
          </TextCondicions>
        )}

        <hr className='border-t-1 border-white mb-4 mt-4 w-full' />

        {/* Título e descrição da seção */}
        <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-2'>
          Condições para negociação.
        </p>
        <p className='text-sm text-[#62829A] tracking-1 mb-4'>
          Selecione uma condição para seguir.
        </p>

        {/* Lista de condições negociadas */}
        <div className='flex flex-col p-4 h-auto'>
          {/* Cards de condições dinâmicas */}
          {negotiatedConditions.length > 0 ? (
            negotiatedConditions.map((installment, index) => (
              <CardNegotiatedConditions
                key={`CardNegotiatedConditions_${index}`}
                index={index}
                total={negotiatedConditions.length - 1}
                installment={installment}
                selectCondiction={selectCondiction}
                condictionSelect={indexSelect as number}
              />
            ))
          ) : (
            <div className='text-center p-4 text-navy-blue'>
              Não há condições de negociação disponíveis no momento.
            </div>
          )}

          {/* Card estático "Quero mais informações" */}
          {shouldShowStaticCard() && (
            <CardNegotiatedConditionsStatic
              title={'Quero receber contato para mais informações.'}
              index={-1}
              selectCondiction={selectCondiction}
              condictionSelect={indexSelect as number}
            />
          )}
        </div>

        {/* Botões de ação */}
        {condictionSelect !== null && (
          <div className='flex justify-center flex-row mb-14 w-1/2 m-auto max-md:w-full'>
            <div className='mr-1'>
              <Buttontransparent color='navy-blue' text='Cancelar' onClick={cancelAction} />
            </div>
            <div className='ml-1'>
              <Buttonblue
                text={textButton}
                background='navy-blue'
                color='white'
                onClick={confirmAction}
              />
            </div>
          </div>
        )}
      </motion.div>
    );
  }
);

Conditions.displayName = 'Conditions';

export default Conditions;
