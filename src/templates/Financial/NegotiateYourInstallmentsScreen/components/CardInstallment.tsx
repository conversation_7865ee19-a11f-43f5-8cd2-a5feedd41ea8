/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import Image from 'next/image';

import { InstallmentDetails, InstallmentItem } from '@/@types/installments';

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { useAppContext } from '@/contexts/AppContext';
import CustomLink from '@/templates/components/Commons/buttons/CustomLink';
import { motion } from 'framer-motion';

interface CardInstallmentProps {
  index: number;
  installment: InstallmentItem;
  detail: InstallmentDetails;
}

const CardInstallment = ({ index, installment, detail }: CardInstallmentProps) => {
  const { user } = useAppContext();
  if (detail === null) return null;
  const {
    statusBoleto,
    duedateFormated,
    typeCondiction,
    paidTotalValue,
    paidValue,
    currentBalanceWithAddition
  } = detail;

  let valueFinal;
  switch (statusBoleto) {
  case 'Pago':
    valueFinal = paidTotalValue;
    break;
  case 'Vencido':
    valueFinal = currentBalanceWithAddition;
    break;
  case 'A vencer':
    valueFinal = paidValue;
    break;
  }
  return (
    <motion.tr
      key={`installment-mobile-${installment.id}`}
      initial='collapsed'
      animate={'open'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{
        duration: 0.7,
        ease: 'easeOut',
        delay: index * 0.1
      }}
      className={`border-b-1 h-[78px] border-white ${index % 2 === 0 ? 'bg-[#f6f6f6]' : ''} ${
        statusBoleto === 'Vencido' ? ' !bg-[#ffe6e6] border-b-2' : ''
      } id-${installment.id}`}>
      <td className='py-3 pl-4 text-navy-blue text-sm tracking-wide text-left w-[35%]'>
        <CustomLink
          href={`/${user?.AccountId}/financeiro/parcela-detalhada/${installment.id}/${installment.parentIndex}`}
          className='w-full'>
          {duedateFormated}
        </CustomLink>
      </td>
      <td className='py-3 text-navy-blue text-sm tracking-wide text-left w-[37%]'>
        <CustomLink
          href={`/${user?.AccountId}/financeiro/parcela-detalhada/${installment.id}/${installment.parentIndex}`}
          className='w-full flex flex-col justify-left'>
          <small className='tracking-wide text-navy-blue font-thin flex text-xs w-[85%]'>
            {typeCondiction}
          </small>
          <p className='font-medium'>{valueFinal}</p>
        </CustomLink>
      </td>
      <td className='py-3  text-navy-blue text-sm tracking-wide text-left w-[20%]'>
        <CustomLink
          href={`/${user?.AccountId}/financeiro/parcela-detalhada/${installment.id}/${installment.parentIndex}`}
          className={`block  w-full tracking-wide  text-blue-secondary font-medium text-left  text-xs ${
            statusBoleto === 'Vencido' ? 'text-salmon' : ''
          }
            ${statusBoleto === 'A vencer' ? 'text-navy-blue' : ''}`}>
          {statusBoleto}
        </CustomLink>
      </td>
      <td className='w-[6%] mr-2'>
        <Image
          src='/images/angle-down.svg'
          alt='Icon'
          className='-rotate-90 w-auto h-auto'
          width={24}
          height={16}
          priority
        />
      </td>
    </motion.tr>
  );
};

export default CardInstallment;
