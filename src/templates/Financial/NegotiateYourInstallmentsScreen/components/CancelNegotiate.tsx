/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { motion } from 'framer-motion';
import React from 'react';

import { useMediaQuery } from '@/hooks/useMediaQuery';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import CustomDropdown from '@/templates/components/Commons/inputs/CustomDropdown';

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { optionsDataCancel } from '../mock/optionsCancelNegotiate';

interface CancelNegotiateProps {
  title: string;
  text: string;
  show: boolean;
  cancelMotivationSelect: (value: string | number) => void;
  close: () => void;
  sendCancel: () => void;
}

const CancelNegotiate: React.FC<CancelNegotiateProps> = ({
  title,
  text,
  show,
  cancelMotivationSelect,
  close,
  sendCancel
}) => {
  const { isMobile } = useMediaQuery();
  if (!show) return null;
  const classTitle = isMobile
    ? 'text-navy-blue font-medium text-xl tracking-1 max-md:hidden'
    : 'font-bold text-base text-navy-blue tracking-1 mb-4 hidden max-md:block';
  return (
    <motion.div
      initial='collapsed'
      animate={'open'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{ duration: 0.5, ease: 'linear' }}>
      <h4 className={classTitle}>{title}</h4>

      <p className='text-sm text-navy-blue tracking-1 mb-4'>{text}</p>
      <div className='w-3/4 flex flex-row items-center max-md:w-full max-md:flex-col'>
        <CustomDropdown
          optionsExternal={optionsDataCancel}
          onChange={(value) => cancelMotivationSelect(value as string | number)}
        />
        <div className='w-full flex flex-row'>
          <div className='w-[45%] flex -mt-6 ml-5 max-md:m-auto'>
            <Buttonblue background='navy-blue' text='Voltar' color='white' onClick={close} />
          </div>
          <div className='w-[45%] flex -mt-6 ml-5 max-md:m-auto'>
            <Buttonblue background='navy-blue' text='Enviar' color='white' onClick={sendCancel} />
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default CancelNegotiate;
