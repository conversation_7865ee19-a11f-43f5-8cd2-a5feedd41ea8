/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import React from 'react';

import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { useAppContext } from '@/contexts/AppContext';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import { motion } from 'framer-motion';

interface ReturnNegotiateProps {
  title: string;
  text: string;
  show: boolean;
}

const ReturnNegotiate: React.FC<ReturnNegotiateProps> = ({ title, text, show }) => {
  const { user } = useAppContext();

  if (!show) return null;

  return (
    <motion.div
      initial='collapsed'
      animate={'open'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{ duration: 0.5, ease: 'linear' }}
      className='flex justify-center flex-col items-center'>
      <p className='font-bold text-base text-navy-blue tracking-1 mb-4 text-center max-md:text-left'>
        {title}
      </p>
      <p
        className='text-sm text-navy-blue tracking-1 text-center max-md:text-left'
        // eslint-disable-next-line react/no-danger
        dangerouslySetInnerHTML={{ __html: text }}
      />
      <div className='w-1/2 flex m-auto'>
        <Buttonblue
          background='navy-blue'
          text='Ver solicitações'
          color='white'
          route={`/${user?.AccountId}/fale-com-a-cury/acompanhar-solicitacoes`}
        />
      </div>
    </motion.div>
  );
};

export default ReturnNegotiate;
