/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { ChangeEvent } from 'react';

import { InstallmentItem, SelectedInstallment, SelectedInstallments } from '@/@types/installments';
import { AnimateFadeIn } from '@/constants/ANIMATIONS';
import { Status } from '@/templates/Financial/Status';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { motion } from 'framer-motion';

interface CardInstallmentProps {
  index: number;
  index2: number;
  installment: InstallmentItem;
  selectInstallments: (selectedInstallment: SelectedInstallment) => void;
  selectedInstallments: SelectedInstallments | null;
}

const CardInstallmentNegotiate = ({
  index,
  index2,
  installment,
  selectInstallments,
  selectedInstallments
}: CardInstallmentProps) => {
  const selected = selectedInstallments
    ? selectedInstallments.selectedInstallment.flat().some((selectedInstallment) => {
      return (
        selectedInstallment.id === (installment.id as unknown as string) &&
          selectedInstallment.billReceivableId === installment.billReceivableId
      );
    })
    : false;
  const titleName = installment?.billReceivableId?.toLocaleString('pt-BR') as string;
  // const dateBoleto: number | null = installment.receipts[0].days;
  const today = new Date().toISOString().split('T')[0];
  const duodatesituation = DateUtils.compareDates(
    today,
    (installment.isoDueDate ?? installment.dueDate) as string
  );
  const statusBoletoIndex: string | null = duodatesituation
    ? 'Vencido'
    : installment.receipts[0].type;
  const key = statusBoletoIndex ?? 'null';
  const statusBoleto = Status[key];
  function changeHandle(_: ChangeEvent<HTMLInputElement>): void {
    const selected: SelectedInstallment = {
      billReceivableId: installment.billReceivableId as number,
      id: installment.id as unknown as string,
      indexerId: installment.indexerId as number,
      isoDueDate: installment.isoDueDate ?? installment.dueDate,
      dueDate: installment.isoDueDate ?? installment.dueDate,
      value: installment.currentBalanceWithAddition
        ? installment.currentBalanceWithAddition
        : installment.originalValue,
      paymentTerms: {
        id: installment.paymentTerms.id,
        description: installment.paymentTerms.descrition,
        descrition: installment.paymentTerms.descrition
      }
    };

    selectInstallments(selected);
  }

  return (
    <motion.tr
      initial='collapsed'
      animate={'open'}
      variants={AnimateFadeIn}
      exit='exit'
      transition={{
        duration: 0.7,
        ease: 'easeOut',
        delay: index * 0.1
      }}
      key={index2}
      className={` border-b-1 border-white ${statusBoleto === 'Vencido' ? 'bg-white' : ''}`}>
      <td className='py-3 pl-5 '>
        <input
          type='checkbox'
          checked={selected}
          value=''
          className='mt-1.5 h-7 w-7 rounded accent-light-blue border-1 border-light-blue'
          disabled={statusBoleto === 'Pago'}
          onChange={changeHandle}
        />
      </td>
      <td className='py-3 pl-5 text-navy-blue text-sm tracking-wide text-center'>{titleName}</td>
      <td className='py-3 pl-5 text-navy-blue text-sm tracking-wide  text-center'>
        {installment.id}
      </td>
      <td className='py-3 text-navy-blue text-sm tracking-wide flex flex-col items-center'>
        <small className='tracking-wide text-navy-blue font-medium flex text-xs text-center'>
          {installment.paymentTerms.descrition}
        </small>
        <p className='font-bold'>
          {FinanceUtils.MoneyFormat.format(
            installment.currentBalanceWithAddition
              ? installment.currentBalanceWithAddition
              : installment.originalValue
          )}
        </p>
      </td>
      <td className='py-3 text-navy-blue text-sm tracking-wide text-left '>
        <small
          className={`tracking-wide  font-medium flex text-xs ${
            statusBoleto === 'Pago' ? 'text-blue-tertiary' : 'text-navy-blue'
          }`}>
          {statusBoleto}
        </small>
        <p className='font-bold'>
          {DateUtils.formatDate((installment.isoDueDate ?? installment.dueDate) as string)}
        </p>
      </td>
    </motion.tr>
  );
};

export default CardInstallmentNegotiate;
