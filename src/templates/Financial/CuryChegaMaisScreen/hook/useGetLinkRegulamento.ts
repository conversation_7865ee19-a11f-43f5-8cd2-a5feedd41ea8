/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useState } from 'react';

import { useApi } from '@/hooks/useApi';

export const useGetLinkRegulamento = () => {
  const [link, setLink] = useState<string | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const { getFetch } = useApi();
  const getLinkRegulamento = async () => {
    try {
      const response = await getFetch<{
        data: {
          link: string;
        };
      }>({
        method: 'GET',
        route: 'chegamais/regulamento'
      });
      if (response instanceof Error) {
        setError(response);
      } else {
        setLink(response?.data?.link as string);
      }
    } catch (e) {
      setError(e as Error);
    }
  };

  return { link, error, getLinkRegulamento };
};
