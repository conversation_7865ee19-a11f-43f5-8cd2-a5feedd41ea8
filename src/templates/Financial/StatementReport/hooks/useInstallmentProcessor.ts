/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { InstallmentDetails, InstallmentItem, Installments } from '@/@types/installments';
import { DateUtils } from '@/utils/DateUtils';
import { FinanceUtils } from '@/utils/FinanceUtils';
import { MiscUtils } from '@/utils/MiscUtils';
import { useCallback, useMemo } from 'react';
import {
  filterInstallment,
  getBoletoStatus,
  getTotalPaidValue,
  prepareInstallmentRowData
} from '../utils/installmentUtils';

interface UseInstallmentProcessorProps {
  installments: Installments | null;
  allIndexer: Array<{ id: number; name: string }>;
  showFn: boolean;
}

export const useInstallmentProcessor = ({
  installments,
  allIndexer,
  showFn
}: UseInstallmentProcessorProps) => {
  // Processa todas as parcelas
  const allInstallments = useMemo(() => {
    if (!installments) return [];

    return installments.data
      .flatMap((subArray) =>
        subArray.installments
          .filter((installment: InstallmentItem) => filterInstallment(installment, showFn))
          .map((installment: InstallmentItem) => {
            const filteredReceipts = installment.receipts.filter((receipt) =>
              ['Recebimento', 'Adiantamento', null].includes(receipt.type)
            );

            if (filteredReceipts.length === 0) {
              return null;
            }

            return {
              ...installment,
              parentIndex: subArray.parentIndex ?? 0,
              billReceivableId: subArray.billReceivableId,
              document: subArray.document,
              indexerName:
                allIndexer.length > 0 ? (allIndexer[installment.indexerId ?? 0]?.name ?? '') : '',
              receipts: filteredReceipts
            };
          })
          .filter((installment: InstallmentItem | null) => installment !== null)
      )
      .sort((a, b) => {
        const dateA = new Date(a?.isoDueDate ?? 0).getTime();
        const dateB = new Date(b?.isoDueDate ?? 0).getTime();
        return dateA - dateB;
      });
  }, [installments, allIndexer, showFn]);

  // Processa detalhes de uma parcela específica
  const installmentDetail = useCallback(
    (id: number, parentIndex: number): InstallmentDetails => {
      let installment = allInstallments?.find(
        (inst) => inst?.id === id // && inst?.parentIndex === parentIndex
      );
      if (!installment) {
        installment = allInstallments?.find(
          (inst) => inst?.id === id && inst?.parentIndex === parentIndex
        );
      }

      if (!installment) return null;

      const {
        installmentSituation,
        billReceivableId,
        receipts,
        document,
        paymentTerms,
        isoDueDate,
        dueDate,
        originalValue,
        indexerId,
        indexerName,
        currentBalance,
        currentBalanceWithAddition,
        generatedBillet
      } = installment;

      // Formata e calcula valores
      const statusBoleto = getBoletoStatus(
        installmentSituation,
        (isoDueDate ?? dueDate) as string,
        receipts,
        currentBalanceWithAddition
      );

      const installmentNumber = id < 10 ? `0${id}` : id;
      const titleName = billReceivableId ? billReceivableId.toLocaleString('pt-BR') : '0';
      const contractType = MiscUtils.getContractType(document);
      const typeCondiction = paymentTerms.descrition;
      const duedateFormated = DateUtils.formatDate((isoDueDate ?? dueDate) as string);
      const receiptDate = receipts[0].date ? DateUtils.formatDate(receipts[0].date) : '-';

      // Calcula o valor da correção
      let correctionValue: number =
        receipts[0].netReceipt !== null
          ? getTotalPaidValue(receipts) - originalValue
          : currentBalance - originalValue;

      correctionValue = correctionValue < 0 ? 0 : correctionValue;
      const formattedCorrectionValue = FinanceUtils.MoneyFormat.format(correctionValue);

      // Calcula juros e multas
      const currentJurosMulta = FinanceUtils.MoneyFormat.format(
        currentBalanceWithAddition - currentBalance
      );

      // Formata valores monetários
      const paidValue = FinanceUtils.MoneyFormat.format(currentBalanceWithAddition);
      const paidTotalValue = FinanceUtils.MoneyFormat.format(getTotalPaidValue(receipts));

      // Prepara dados para a tabela
      const rowsData = prepareInstallmentRowData(
        id,
        titleName,
        contractType,
        typeCondiction,
        duedateFormated,
        receiptDate,
        FinanceUtils.MoneyFormat.format(originalValue),
        `${indexerId}-${indexerName}`,
        formattedCorrectionValue,
        currentJurosMulta,
        FinanceUtils.MoneyFormat.format(currentBalanceWithAddition),
        paidValue,
        paidTotalValue,
        statusBoleto
      );

      return {
        billReceivableId,
        installmentNumber,
        titleName,
        contractType,
        typeCondiction,
        duedateFormated,
        receiptDate,
        originalValue: FinanceUtils.MoneyFormat.format(originalValue),
        indexerName: allIndexer.length > 0 ? `${indexerId}-${allIndexer[indexerId ?? 0].name}` : '',
        correctionValue: formattedCorrectionValue,
        currentJurosMulta,
        currentBalanceWithAddition: FinanceUtils.MoneyFormat.format(currentBalanceWithAddition),
        paidValue,
        paidTotalValue,
        statusBoleto,
        rowsData,
        generatedBillet
      };
    },
    [allInstallments, allIndexer]
  );

  // Processa as parcelas para 2a via do boleto
  const copybillData = useMemo(() => {
    if (!installments) return null;

    return installments.data
      .flatMap((subArray, parentIndex) =>
        subArray.installments.map((installment) => ({
          ...installment,
          parentIndex,
          billReceivableId: subArray.billReceivableId,
          document: subArray.document,
          indexerName: allIndexer.length > 0 ? allIndexer[installment?.indexerId ?? 0].name : ''
        }))
      )
      .filter(
        (installment) =>
          installment.generatedBillet &&
          installment.installmentSituation === '0' &&
          installment.receipts[0]?.type !== 'Reparcelamento' &&
          installment.receipts[0]?.type !== 'Substituição'
      )
      .sort((a, b) => {
        const dateA = new Date((a.isoDueDate ?? a.dueDate) as string).getTime();
        const dateB = new Date((b.isoDueDate ?? b.dueDate) as string).getTime();
        return dateA - dateB;
      });
  }, [installments, allIndexer]);

  return {
    allInstallments,
    installmentDetail,
    copybillData
  };
};
