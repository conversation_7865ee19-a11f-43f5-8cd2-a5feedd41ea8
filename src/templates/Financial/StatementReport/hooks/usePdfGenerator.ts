/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { Financials } from '@/@types/financial';
import { InstallmentItem } from '@/@types/installments';
import { useApi, useModal } from '@/hooks';
import { useCallback } from 'react';
import { PdfService } from '../services/PdfService';

interface UsePdfGeneratorProps {
  user: {
    Name?: string;
    CPF__c?: string;
    TelefoneCelular__c?: string;
    Email__c?: string;
    CodigoSienge__c?: string;
  } | null;
  content: {
    Empreendimento?: {
      name?: string;
    };
    UnidadeAtivoName?: string;
    Contract?: {
      DataCompra__c?: string;
    };
  } | null;
  financials: Financials | null;
  allInstallments: InstallmentItem[];
  installmentDetailFn: (id: number, parentIndex: number) => any;
}

export const usePdfGenerator = ({
  user,
  content,
  financials,
  allInstallments,
  installmentDetailFn
}: UsePdfGeneratorProps) => {
  const { toggleModal } = useModal();
  const { getFetch } = useApi();

  /**
   * Gerencia a ação de exportação do PDF (download ou email)
   */
  const handlePdfAction = useCallback(
    async (type: 'download' | 'email') => {
      // Exibe modal de processamento
      toggleModal({
        text:
          type === 'download'
            ? 'Download iniciado. Por favor, aguarde.'
            : 'Enviando extrato para seu email. Por favor, aguarde.'
      });

      try {
        // Gera o documento PDF
        const { doc, docname } = await PdfService.generatePdfDocument({
          user,
          content,
          financials,
          allInstallments,
          installmentDetailFn,
          getFetch
        });

        // Executa a ação solicitada
        if (type === 'download') {
          await PdfService.downloadPdf(doc, docname);
          toggleModal({ text: 'Download concluído com sucesso!' });
        } else {
          const success = await PdfService.emailPdf(doc, docname, user, getFetch);
          toggleModal({
            text: success
              ? 'Extrato enviado para seu email com sucesso!'
              : 'Não foi possível enviar o extrato para seu email. Tente novamente mais tarde.'
          });
        }
      } catch (error) {
        console.error('Erro ao processar PDF:', error);
        toggleModal({
          text: 'Ops... Ocorreu um erro ao gerar o extrato. Tente novamente mais tarde.'
        });
      }
    },
    [user, content, financials, allInstallments, installmentDetailFn, toggleModal, getFetch]
  );

  return {
    action: handlePdfAction
  };
};
