/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
// import { FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { User } from '@/@types/user';
import { AnimateFadeInOpenHeight } from '@/constants/ANIMATIONS';
import Input from '@/templates/components/Commons/inputs/Input';

import { FieldErrors, UseFormRegister, UseFormSetValue, UseFormWatch } from 'react-hook-form';
import { useUserData } from '../hooks/useUserData';

interface Props {
  register: UseFormRegister<User>;
  errors: FieldErrors<User>;
  watch: UseFormWatch<User>;
  setValue: UseFormSetValue<User>;
  showAddress: boolean;
  setShowAddress: Dispatch<SetStateAction<boolean>>;
}
interface CepDataProps {
  neighborhood: string;
  street: string;
  city: string;
  state: string;
  postalCode: string;
}
export default function AdressInfo({
  register,
  errors,
  watch,
  setValue,
  showAddress,
  setShowAddress
}: Readonly<Props>) {
  const [firstRender, setFirstRender] = useState(true);
  const [cepChanged, setCepChanged] = useState(false);
  const { user, handleCepChange } = useUserData();

  const cep = watch('ShippingPostalCode__c');
  useEffect(() => {
    if (firstRender) {
      setFirstRender(false);
      return;
    }

    const getCep = async () => {
      const cepData = (await handleCepChange(cep)) as CepDataProps;
      try {
        if (cepData) {
          setValue('ShippingNeighborhood__c', cepData.neighborhood);
          setValue('ShippingStreet__c', cepData.street);
          setValue('ShippingCity__c', cepData.city);
          setValue('ShippingState__c', cepData.state);
          setValue('ShippingPostalCode__c', cepData.postalCode);
        }
      } catch (err) {
        console.log(err);
      }
    };
    const cepfinal = cep.replace('-', '').replace('_', '');

    if (cepChanged && cepfinal.length === 8) {
      getCep();
    }
  }, [cep, cepChanged]);

  useEffect(() => {
    if (!firstRender) {
      setCepChanged(true);
    }
  }, [cep]);
  if (!user) return null;
  return (
    <>
      <button
        className='h-12 bg-white border-l-15 border-neutral-blue rounded flex flex-row items-center justify-between px-4 mt-5'
        onClick={() => setShowAddress(!showAddress)}
        type='button'>
        <p className='text-base text-navy-blue tracking-1 leading-4'>Informações de Endereço</p>
        {showAddress ? (
          <Image
            src='/images/less.svg'
            alt='Icon'
            className='rounded-full'
            width={17}
            height={17}
            priority
          />
        ) : (
          <Image
            src='/images/more.svg'
            alt='Icon'
            className='rounded-full'
            width={17}
            height={17}
            priority
          />
        )}
      </button>

      <AnimatePresence>
        {showAddress && (
          <motion.div
            initial='collapsed'
            animate={showAddress ? 'open' : 'collapsed'}
            variants={AnimateFadeInOpenHeight}
            exit='exit'
            transition={{ duration: 0.3, ease: 'easeInOut' }}>
            <div className='bg-white pt-1 px-4 pb-6 rounded'>
              <Input
                label='CEP:'
                type='textmask'
                {...register('ShippingPostalCode__c', {
                  required: 'CEP é obrigatória'
                })}
                mask='99999-999'
                defaultValue={user?.ShippingPostalCode__c ?? ''}
                errors={errors.ShippingPostalCode__c ? errors.ShippingPostalCode__c.message : null}
              />

              <Input
                label='Logradouro:'
                {...register('ShippingStreet__c', {
                  required: 'Logradouro é obrigatória'
                })}
                errors={errors.ShippingStreet__c ? errors.ShippingStreet__c.message : null}
                type='text'
                disabled={true}
              />

              <Input
                label='Número:'
                {...register('ShippingNumber__c', {
                  required: 'Número é obrigatória'
                })}
                type='number'
                errors={errors.ShippingNumber__c ? errors.ShippingNumber__c.message : null}
                maxLength={10}
              />

              <Input label='Complemento:' {...register('ShippingComplement__c')} type='text' />

              <Input
                label='Bairro:'
                {...register('ShippingNeighborhood__c', {
                  required: 'Bairro é obrigatória'
                })}
                errors={
                  errors.ShippingNeighborhood__c ? errors.ShippingNeighborhood__c.message : null
                }
                type='text'
                disabled={true}
              />

              <Input
                label='Cidade:'
                {...register('ShippingCity__c', {
                  required: 'Cidade é obrigatória'
                })}
                errors={errors.ShippingCity__c ? errors.ShippingCity__c.message : null}
                type='text'
                disabled={true}
              />

              <Input
                label='Estado:'
                {...register('ShippingState__c', {
                  required: 'Estado é obrigatória'
                })}
                errors={errors.ShippingState__c ? errors.ShippingState__c.message : null}
                type='text'
                disabled={true}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
