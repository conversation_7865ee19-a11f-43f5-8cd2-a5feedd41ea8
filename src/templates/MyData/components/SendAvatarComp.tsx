/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import { useRef, useState } from 'react';

import { AnimateFadeInOpenHeight } from '@/constants/ANIMATIONS';
import { Buttonblue } from '@/templates/components/Commons/buttons/Buttonblue';
import Buttontransparent from '@/templates/components/Commons/buttons/Buttontransparent';
import { useUserData } from '../hooks/useUserData';

interface SendAvatarCompProps {
  showUploadImage: boolean;
  setShowUploadImage: React.Dispatch<React.SetStateAction<boolean>>;
}
export default function SendAvatarComp({
  showUploadImage,
  setShowUploadImage
}: Readonly<SendAvatarCompProps>) {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleButtonClick = () => {
    if (fileInputRef.current) fileInputRef.current.click();
  };

  const [sendImage, setSendImage] = useState(false);

  const { avatar, handleUpdateAvatar, error } = useUserData();

  const handleAvatarChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return null;
    await handleUpdateAvatar(file);
    setSendImage(true);
    return true;
  };

  if (!avatar || avatar.startsWith('Account')) return null;
  return (
    <AnimatePresence>
      {showUploadImage && (
        <motion.div
          initial='collapsed'
          animate={showUploadImage ? 'open' : 'collapsed'}
          variants={AnimateFadeInOpenHeight}
          // variants={AnimateFadeIn}
          exit='exit'
          transition={{ duration: 0.3, ease: 'easeInOut' }}>
          {error && <p className='text-red-600'>{error.message}</p>}
          <div className=' bg-[#e5e7ea]  w-full h-auto  pt-1 px-4 pb-6 rounded flex p-top-[100px] items-center flex-col z-[100000]'>
            <p className='text-sm text-navy-blue tracking-1 leading-5 mt-4 w-[75%]  text-center'>
              É possível carregar um arquivo JPG, GIF ou PNG.
            </p>
            <p className='text-sm text-navy-blue tracking-1 leading-5  w-[75%]  text-center'>
              O tamanho máximo do arquivo é de 16MB.
            </p>
            <p className='text-sm text-navy-blue tracking-1 leading-5  w-[75%] mb-10 text-center'>
              Foto atual:
            </p>
            <div className='bg-slate-300 flex justify-center items-center w-[100%] h-[35%] mb-10 p-4 '>
              <div className='flex justify-center items-center w-full h-full overflow-hidden '>
                <Image src={avatar} alt='Icon' className='m-4' width={160} height={160} priority />
              </div>
            </div>

            <input
              type='file'
              name='file'
              ref={fileInputRef}
              onChange={handleAvatarChange}
              className='hidden'
              lang='pt-BR'
            />
            <button
              onClick={handleButtonClick}
              className='flex items-center w-full h-[50px] text-[1rem] bg-[#fff] rounded-[10px]'>
              <span className='bg-[#62829A] mr-[14px] h-[50px] rounded-tl-[10px] rounded-bl-[10px] flex items-center justify-center w-[60px]'>
                <svg
                  width='31'
                  height='31'
                  viewBox='0 0 31 31'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'>
                  <path
                    d='M29.7084 24.5417C29.7084 25.2268 29.4362 25.8839 28.9518 26.3684C28.4673 26.8528 27.8102 27.125 27.1251 27.125H3.87508C3.18994 27.125 2.53286 26.8528 2.04839 26.3684C1.56392 25.8839 1.29175 25.2268 1.29175 24.5417V10.3333C1.29175 9.64819 1.56392 8.99111 2.04839 8.50664C2.53286 8.02217 3.18994 7.75 3.87508 7.75H9.04175L11.6251 3.875H19.3751L21.9584 7.75H27.1251C27.8102 7.75 28.4673 8.02217 28.9518 8.50664C29.4362 8.99111 29.7084 9.64819 29.7084 10.3333V24.5417Z'
                    stroke='white'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  />
                  <path
                    d='M15.4999 21.9583C18.3534 21.9583 20.6666 19.6451 20.6666 16.7917C20.6666 13.9382 18.3534 11.625 15.4999 11.625C12.6464 11.625 10.3333 13.9382 10.3333 16.7917C10.3333 19.6451 12.6464 21.9583 15.4999 21.9583Z'
                    stroke='white'
                    strokeLinecap='round'
                    strokeLinejoin='round'
                  />
                </svg>
              </span>
              <span>Enviar Foto</span>
            </button>

            <div className='flex justify-center items-center flex-row'>
              <div className='w-1/2 mr-1 flex justify-center md:justify-end max-w-[160px]'>
                <Buttontransparent
                  color='navy-blue'
                  text='Cancelar'
                  onClick={() => setShowUploadImage(false)}
                />
              </div>
              <div className='w-1/2 ml-1 flex justify-center md:justify-start max-w-[160px]'>
                <Buttonblue
                  text='Salvar'
                  background='navy-blue'
                  color='white'
                  disabled={!sendImage}
                  type='button'
                  classExtra={'max-w-[160px]'}
                  onClick={() => setShowUploadImage(false)}
                />
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
