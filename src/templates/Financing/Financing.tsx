/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { AnimatePresence, motion } from 'framer-motion';
import Image from 'next/image';
import React, { useEffect, useLayoutEffect, useState } from 'react';

import { Article } from '@/@types/articles';
import { AnimateFadeInOpenHeight } from '@/constants/ANIMATIONS';

import { useAppContext } from '@/contexts/AppContext';
import { useLoading } from '@/hooks';
import { useApi } from '@/hooks/useApi';
import { useCanBeRender } from '@/hooks/useCanBeRender';
import { usePermissions } from '@/hooks/usePermissions';
import InputTransparent from '@/templates/components/Commons/inputs/InputTransparent';
import { StringUtils } from '@/utils/StringUtils';
import SpinnerLoading from '../components/Commons/Loading/SpinnerLoading';
import TitlePage from '../components/Commons/TitlePage';
import DefaultNoContent from '../components/DefaultNoContent/DefaultNoContent';

// Componente para a seção de contato do correspondente
const CorrespondentContact: React.FC<{
  nomeCCA: string;
  telefoneCelularCCA: string;
}> = ({ nomeCCA, telefoneCelularCCA }) => (
  <>
    <div className='w-2/3 max-md:w-full'>
      <p className='font-bold text-base text-navy-blue tracking-1 my-4'>
        Contato do correspondente
      </p>
      <p className='text-sm text-navy-blue tracking-1 mb-4'>
        Para dúvidas sobre seu processo de financiamento bancário <br /> entre em contato com o seu
        correspondente:
      </p>
    </div>

    <div className='flex flex-row max-md:flex-col'>
      <div className='w-1/3 max-md:w-full'>
        <InputTransparent label='' type='text' defaultValue={nomeCCA} />
      </div>
      <div className='max-md:w-full'>
        <div className='mt-4 ml-5 max-md:ml-0 max-md:mb-6'>
          <a href={`tel:${StringUtils.FormatPhoneNumber(telefoneCelularCCA)}`}>
            <div className='h-12 bg-white border-l-15 border-neutral-blue rounded flex flex-row items-center justify-between px-2 mt-[18px]'>
              <p className='text-sm text-navy-blue tracking-1 leading-4 mr-4'>
                Ligar para meu correspondente
              </p>

              <Image
                src='/images/angle-down.svg'
                alt='Icon'
                className='-rotate-90 max-md:hidden object-contain w-auto h-auto'
                width={12}
                height={8}
                priority
              />
            </div>
          </a>
        </div>
      </div>
    </div>
    <hr className='border-t-1 border-white mb-6 mt-2 w-full' />
  </>
);

// Componente para um artigo individual de financiamento
const FinancingArticle: React.FC<{
  article: Article;
  index: number;
  showStep: number;
  setShowStep: (step: number) => void;
}> = ({ article, index, showStep, setShowStep }) => (
  <React.Fragment>
    {index === 0 && (
      <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-4'>
        Etapas do financiamento
      </p>
    )}
    {index === 1 && (
      <p className='font-bold text-base text-navy-blue tracking-1 mt-10 mb-4'>Próximos passos</p>
    )}
    <button
      className='flex w-full h-12 bg-white border-l-15 border-neutral-blue rounded flex-row items-center justify-between px-4 mt-5'
      onClick={() => setShowStep(showStep === index ? -1 : index)}>
      <p className='text-base text-navy-blue tracking-1 leading-4'>{article.Title}</p>
      {showStep === index ? (
        <Image
          src='/images/less.svg'
          alt='Icon'
          className='rounded-full'
          width={17}
          height={17}
          priority
        />
      ) : (
        <Image
          src='/images/more.svg'
          alt='Icon'
          className='rounded-full'
          width={17}
          height={17}
          priority
        />
      )}
    </button>
    <AnimatePresence>
      {showStep === index && (
        <motion.div
          initial='collapsed'
          animate={showStep !== -1 ? 'open' : 'collapsed'}
          variants={AnimateFadeInOpenHeight}
          exit='exit'
          transition={{
            duration: 0.3,
            ease: 'easeInOut'
          }}
          className='bg-white rounded overflow-hidden'>
          <p
            className='text-sm pt-4 px-4 pb-6 text-navy-blue tracking-1 mb-1 mt-4'
            // eslint-disable-next-line react/no-danger
            dangerouslySetInnerHTML={{
              __html: article.Assunto__c
            }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  </React.Fragment>
);

// Componente para listar todos os artigos de financiamento
const FinancingArticlesList: React.FC<{
  articles: Article[] | null;
  totalArticles: number | null;
  showStep: number;
  setShowStep: (step: number) => void;
}> = ({ articles, totalArticles, showStep, setShowStep }) => {
  if (!articles && totalArticles !== null && totalArticles !== 0) {
    return (
      <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-4'>
        <SpinnerLoading />
      </p>
    );
  }

  if (!articles && totalArticles === 0) {
    return <DefaultNoContent text='Não foi possível encontrar nenhum financiamento.' />;
  }

  if (articles && articles.length === 0) {
    return (
      <p className='font-bold text-base text-navy-blue tracking-1 mt-2 mb-4'>
        Em breve você verá as etapas do financiamento
      </p>
    );
  }

  return (
    <>
      {articles &&
        articles.map((article, index) => (
          <FinancingArticle
            key={`${article.Assunto__c}${index}`}
            article={article}
            index={index}
            showStep={showStep}
            setShowStep={setShowStep}
          />
        ))}
    </>
  );
};

// Hook personalizado para carregar os artigos de financiamento
const useFinancingArticles = (content: any) => {
  const [articles, setArticles] = useState<Article[] | null>(null);
  const [totalArticles, setTotalArticles] = useState<number | null>(null);
  const { getFetch } = useApi();

  useEffect(() => {
    const loadData = async () => {
      if (content) {
        const apps = await getFetch<{
          data: Article[];
          success: boolean;
        }>({
          method: 'GET',
          route: `knowledge/showByEtapa/${content.ProposalId}`
        });

        if (apps) {
          setArticles(apps.data);
          setTotalArticles(apps.data.length);
        }
      }
    };

    loadData();
  }, []);

  return { articles, totalArticles };
};

// Hook personalizado para inicializar a UI
const useInitializeUI = (
  setSubtitleHeaderMobile: (subtitle: string) => void,
  setShowSidebar: (show: boolean) => void,
  isMobile: boolean | null,
  setLoadingInfo: (loading: boolean) => void
) => {
  useLayoutEffect(() => {
    setSubtitleHeaderMobile('Financiamento');
    if (isMobile !== null) {
      setShowSidebar(!isMobile);
    }
  }, [isMobile]);

  useEffect(() => {
    setLoadingInfo(false);
  }, []);
};

// Componente principal com complexidade reduzida
export default function FinancingScreen() {
  const [showStep, setShowStep] = useState(0);
  const { setSubtitleHeaderMobile, setShowSidebar, content, isMobile } = useAppContext();
  const { getPermission } = usePermissions(content);
  const { canBeRender } = useCanBeRender();
  const { setLoadingInfo } = useLoading();

  // Usar hooks personalizados para reduzir a complexidade
  const { articles, totalArticles } = useFinancingArticles(content);
  useInitializeUI(setSubtitleHeaderMobile, setShowSidebar, isMobile, setLoadingInfo);

  // Verificações de renderização
  const conditions = [isMobile === null, content === null];
  if (!canBeRender({ conditions })) return null;

  // Verificar permissões
  const canViewContato = getPermission('Contato do correspondente');
  const hasCorrespondentContact =
    canViewContato && content?.NomeCCA__c && content?.TelefoneCelularCCA__c;

  return (
    <>
      {isMobile ? null : <TitlePage text='Financiamento' />}

      {hasCorrespondentContact && (
        <CorrespondentContact
          nomeCCA={content.NomeCCA__c as string}
          telefoneCelularCCA={content.TelefoneCelularCCA__c as string}
        />
      )}

      <div className='mb-10'>
        <FinancingArticlesList
          articles={articles}
          totalArticles={totalArticles}
          showStep={showStep}
          setShowStep={setShowStep}
        />
      </div>
    </>
  );
}
