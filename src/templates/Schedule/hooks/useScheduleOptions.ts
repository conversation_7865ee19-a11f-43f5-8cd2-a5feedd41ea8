/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

import { useEffect, useMemo, useState } from 'react';

import {
  Holidays,
  ScheduleOptionsResponse,
  ScheduleServiceOptions,
  WorkOrders
} from '@/@types/Schedule';
import { apiGet } from '@/server/services/api';

interface GetScheduleProps {
  EmpreendimentoId: string;
  isActive: boolean;
}

export const useScheduleOptions = ({ EmpreendimentoId, isActive }: GetScheduleProps) => {
  const [schedule, setSchedule] = useState<ScheduleServiceOptions[] | null>(null);
  const [holidays, setHolidays] = useState<Holidays[]>([{ Name: '', NextOccurrenceDate: '' }]);
  const [busyDays, setBusyDays] = useState<string[]>([]);
  const [workOrders, setWorkOrders] = useState<WorkOrders[]>([{ StartDate: '' }]);
  // const { getFetch } = useApi();
  // const [serviceTerritoryMember, setServiceTerritoryMember] = useState<
  //   ServiceTerritoryMember[] | null
  // >(null);
  const [dayOfWeek, setDayOfWeek] = useState<string>('');
  const [dateSeleted, setDateSeleted] = useState<string>('00/00/0000');
  const [error] = useState<Error | null>(null);

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        // const response = await getFetch<ScheduleOptionsResponse>({
        //   method: 'GET',
        //   route: `scheduleServiceOptions?empreendimentoId=${EmpreendimentoId}&isActive=${isActive}`,
        //   cache: 'no-store'
        // });
        const response = await apiGet<ScheduleOptionsResponse>(
          `scheduleServiceOptions?empreendimentoId=${EmpreendimentoId}&isActive=${isActive}`
        );
        if (response?.data) {
          setSchedule(response.data.schedule);
          // if (response.data.schedule) {
          //   setSchedule([
          //     ...response.data.schedule,
          //     {
          //       DayOfWeek: 'Wednesday',
          //       EmpreendimentoId: 'a1E1U000002Pm7cUAC',
          //       EndTime: '10:30:00.000Z',
          //       ScheduleServicesOptionsId: '0Gj4Q000200g1woSAA',
          //       StartTime: '10:00:00.000Z',
          //       TimeSlotNumber: 'TS-16881'
          //     }
          //   ]);
          // }
          setHolidays(response.data.holidays);
          setWorkOrders(response.data.workOrders);
          // setWorkOrders((prev) => [...prev, { StartDate: '2025-04-18T10:00:00.000+0000' }]);
        } else {
          // setError(response);
          return response;
        }
      } catch (err: unknown) {
        // setError(err);
        return err;
      }
      return true;
    };

    fetchSchedule();
  }, []);

  const getStartTimes = () => {
    if (!schedule || schedule.length === 0) return [];
    const today = new Date();
    const maxAllowedDate = new Date(today);
    maxAllowedDate.setDate(today.getDate() + 30);
    const totalDaysOfWeek = [0, 0, 0, 0, 0, 0, 0];
    schedule.forEach((item) => {
      switch (item.DayOfWeek) {
        case 'Sunday':
          totalDaysOfWeek[0]++;
          break;
        case 'Monday':
          totalDaysOfWeek[1]++;
          break;
        case 'Tuesday':
          totalDaysOfWeek[2]++;
          break;
        case 'Wednesday':
          totalDaysOfWeek[3]++;
          break;
        case 'Thursday':
          totalDaysOfWeek[4]++;
          break;
        case 'Friday':
          totalDaysOfWeek[5]++;
          break;
        case 'Saturday':
          totalDaysOfWeek[6]++;
          break;
        default:
          break;
      }
    });
    let indexDay = 0;

    const totalDays: number[] = [];
    for (
      let newDate = new Date(today);
      newDate <= maxAllowedDate;
      newDate.setDate(newDate.getDate() + 1)
    ) {
      const [day, month, year] = newDate.toLocaleDateString('pt-BR').split('/').map(Number);
      const selectedDate = new Date(year, month - 1, day);
      totalDays[indexDay] = totalDaysOfWeek[selectedDate.getDay()];
      indexDay++;
    }
    let indexDayVerifyToBusy = 0;
    for (
      let newDate = new Date(today);
      newDate <= maxAllowedDate;
      newDate.setDate(newDate.getDate() + 1)
    ) {
      const [day, month, year] = newDate.toLocaleDateString('pt-BR').split('/').map(Number);
      const selectedDate = new Date(year, month - 1, day);

      const dayOfWeek = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
      ][selectedDate.getDay()];

      schedule.filter((item) => {
        if (item.DayOfWeek !== dayOfWeek) return false;
        const itemDateTime = new Date(
          year,
          month - 1,
          day,
          parseInt(item.StartTime.slice(0, 2), 10),
          parseInt(item.StartTime.slice(3, 5), 10)
        );

        workOrders.some((workOrder) => {
          const workOrderDate = new Date(
            new Date(workOrder.StartDate).getTime() + 0 * 60 * 60 * 1000
          );
          if (itemDateTime.toString() === workOrderDate.toString()) {
            totalDays[indexDayVerifyToBusy]--;
            if (totalDays[indexDayVerifyToBusy] === 0) {
              setBusyDays((prev) => [...prev, new Date(itemDateTime).toISOString().slice(0, 10)]);
            }
          }
        });
      });
      indexDayVerifyToBusy++;

      //   const hour = item.StartTime.slice(0, 5);
      //   const formattedTime = hour.slice(0, 5) + 'h';
      //   const formattedTimeGtm = (parseInt(hour.slice(0, 2), 10) + 3).toString() + hour.slice(2);

      //   return {
      //     id: index,
      //     name: formattedTime,
      //     value: formattedTimeGtm
      //   };
      // });
    }
  };
  const getStartTimesByDay = (dateSelected: string) => {
    if (!schedule || schedule.length === 0 || !dateSelected) return [];

    const [day, month, year] = dateSelected.split('/').map(Number);
    const selectedDate = new Date(year, month - 1, day);
    const dayOfWeek = [
      'Sunday',
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday'
    ][selectedDate.getDay()];

    const filteredData = schedule.filter((item) => {
      if (item.DayOfWeek !== dayOfWeek) return false;

      const itemDateTime = new Date(
        year,
        month - 1,
        day,
        parseInt(item.StartTime.slice(0, 2), 10),
        parseInt(item.StartTime.slice(3, 5), 10)
      );

      const isUnavailable = workOrders.some((workOrder) => {
        const workOrderDate = new Date(
          new Date(workOrder.StartDate).getTime() + 0 * 60 * 60 * 1000
        );
        return (
          new Date(itemDateTime).toISOString().slice(11, 16) ===
            new Date(workOrderDate).toISOString().slice(11, 16) &&
          new Date(itemDateTime).toISOString().slice(0, 10) ===
            new Date(workOrderDate).toISOString().slice(0, 10)
        );
      });

      return !isUnavailable;
    });
    return filteredData.map((item, index) => {
      const hour = item.StartTime.slice(0, 5);
      const formattedTime = hour.slice(0, 5) + 'h';
      const formattedTimeGtm = (parseInt(hour.slice(0, 2), 10) + 3).toString() + hour.slice(2);

      return {
        id: index,
        name: formattedTime,
        value: formattedTimeGtm
      };
    });
  };

  const allowedDaysOfWeek = useMemo(() => {
    if (!schedule || schedule.length === 0) return new Set<number>();
    const days = new Set<number>();
    schedule.forEach((slot) => {
      switch (slot.DayOfWeek) {
        case 'Monday':
          days.add(1);
          break;
        case 'Tuesday':
          days.add(2);
          break;
        case 'Wednesday':
          days.add(3);
          break;
        case 'Thursday':
          days.add(4);
          break;
        case 'Friday':
          days.add(5);
          break;
        case 'Saturday':
          days.add(6);
          break;
        case 'Sunday':
          days.add(0);
          break;
        default:
          break;
      }
    });
    getStartTimes();
    return days;
  }, [schedule]);

  return {
    schedule,
    error,
    allowedDaysOfWeek,
    getStartTimesByDay,
    dayOfWeek,
    setDayOfWeek,
    dateSeleted,
    setDateSeleted,
    holidays,
    busyDays,
    getStartTimes
    // serviceTerritoryMember
  };
};
