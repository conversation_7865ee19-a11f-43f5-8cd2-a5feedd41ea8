/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Content } from '@/@types/content';
import { User } from '@/@types/user';
import { FILES_URL } from '@/constants';
import { useMediaQuery } from '@/hooks';
import { getServerContent, setContentAction } from '@/server/actions';
import { checkStatusBoletoAto } from '@/server/actions/content';
import { useRouter } from 'next/navigation';
import { createContext, ReactNode, useCallback, useContext, useMemo, useReducer } from 'react';

interface UserState {
  user: User | null;
  avatar: string;
}

interface ContentState {
  contents: Content[] | null;
  content: Content | null;
  contentSelected: number;
  imagesToSlider: string[] | null;
  videosToSlider: string[] | null;
  showFoto: number | null;
  showVideos: number | null;
  currentPage: number;
  hasMoreContent: boolean;
  isLoadingMore: boolean;
}

interface UIState {
  isLoading: boolean;
  showSidebar: boolean;
  modals: {
    isVisible: boolean;
    isCookieVisible: boolean;
  };
  mobile: {
    subtitleHeader: string;
  };
}

// Actions
type Action =
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_CONTENT'; payload: Content | null }
  | { type: 'SET_CONTENTS'; payload: Content[] | null }
  | { type: 'SET_CONTENT_SELECTED'; payload: number }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_UI_STATE'; payload: Partial<UIState> }
  | { type: 'SET_MODAL_STATE'; payload: { type: 'default' | 'cookie'; value: boolean } }
  | { type: 'SET_SHOW_FOTO'; payload: number | null }
  | { type: 'SET_SHOW_VIDEOS'; payload: number | null }
  | { type: 'SET_IMAGES_TO_SLIDER'; payload: string[] | null }
  | { type: 'SET_VIDEOS_TO_SLIDER'; payload: string[] | null }
  | { type: 'SET_SUBTITLE_HEADER_MOBILE'; payload: string }
  | { type: 'SET_CURRENT_PAGE'; payload: number }
  | { type: 'SET_HAS_MORE_CONTENT'; payload: boolean }
  | { type: 'SET_LOADING_MORE'; payload: boolean }
  | { type: 'APPEND_CONTENTS'; payload: Content[] };

// Reducers
const userReducer = (state: UserState, action: Action): UserState => {
  switch (action.type) {
    case 'SET_USER': {
      const avatarsrc = action.payload?.avatar
        ? action.payload?.avatar?.startsWith('http')
          ? action.payload?.avatar
          : `${FILES_URL}${action.payload?.avatar}`
        : '/images/perfil-default.svg';

      // const pathAvatar = action.payload?.avatar.startsWith('http')
      //   ? action.payload?.avatar
      //   : `${FILES_URL}${action.payload?.avatar}`;

      return {
        ...state,
        user: action.payload,
        avatar: avatarsrc
      };
    }
    default:
      return state;
  }
};

const contentReducer = (state: ContentState, action: Action): ContentState => {
  switch (action.type) {
    case 'SET_CONTENT':
      return { ...state, content: action.payload };
    case 'SET_CONTENTS':
      return { ...state, contents: action.payload };
    case 'SET_CONTENT_SELECTED':
      return { ...state, contentSelected: action.payload };
    case 'SET_SHOW_FOTO':
      return { ...state, showFoto: action.payload };
    case 'SET_SHOW_VIDEOS':
      return { ...state, showVideos: action.payload };
    case 'SET_IMAGES_TO_SLIDER':
      return { ...state, imagesToSlider: action.payload };
    case 'SET_VIDEOS_TO_SLIDER':
      return { ...state, videosToSlider: action.payload };
    case 'SET_CURRENT_PAGE':
      return { ...state, currentPage: action.payload };
    case 'SET_HAS_MORE_CONTENT':
      return { ...state, hasMoreContent: action.payload };
    case 'SET_LOADING_MORE':
      return { ...state, isLoadingMore: action.payload };
    case 'APPEND_CONTENTS': {
      // Evita duplicações verificando se os itens já existem no array
      if (!state.contents) {
        return { ...state, contents: action.payload };
      }

      // Filtra apenas os novos itens que ainda não existem no array
      const existingIds = new Set(state.contents.map((item) => item.ProposalId || item.ContractId));
      const newItems = action.payload.filter(
        (item) => !existingIds.has(item.ProposalId || item.ContractId)
      );

      // Só adiciona se houver novos itens
      if (newItems.length === 0) {
        return state;
      }

      return {
        ...state,
        contents: [...state.contents, ...newItems]
      };
    }
    default:
      return state;
  }
};

const uiReducer = (state: UIState, action: Action): UIState => {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'SET_UI_STATE':
      return { ...state, ...action.payload };
    case 'SET_MODAL_STATE':
      return {
        ...state,
        modals: {
          ...state.modals,
          [action.payload.type === 'cookie' ? 'isCookieVisible' : 'isVisible']: action.payload.value
        }
      };
    case 'SET_SUBTITLE_HEADER_MOBILE':
      return {
        ...state,
        mobile: {
          ...state.mobile,
          subtitleHeader: action.payload
        }
      };
    default:
      return state;
  }
};

interface AppProviderProps {
  children: ReactNode;
  initialUser: User | null;
  initialContents: Content[] | null;
  initialContentSelected: number;
  initialContent: Content | null;
  environment?: string;
}

// Cria o contexto com um valor padrão
const AppContext = createContext<any>({} as any);

// Hook para usar o contexto
export const useAppContext = () => useContext(AppContext);

// Provedor do contexto
export function AppProvider({
  children,
  initialUser,
  initialContents,
  initialContentSelected,
  initialContent,
  environment
}: AppProviderProps) {
  const router = useRouter();
  const { isMobile } = useMediaQuery();
  const [userState, userDispatch] = useReducer(userReducer, {
    user: initialUser,
    avatar: initialUser?.avatar
      ? initialUser.avatar.startsWith('http')
        ? initialUser.avatar
        : `${FILES_URL}${initialUser.avatar}`
      : '/images/perfil-default.svg'
  });

  const [contentState, contentDispatch] = useReducer(contentReducer, {
    contents: initialContents,
    content: initialContent,
    contentSelected: initialContentSelected,
    imagesToSlider: null,
    videosToSlider: null,
    showFoto: null,
    showVideos: null,
    currentPage: 1,
    hasMoreContent: true,
    isLoadingMore: false
  });

  const [uiState, uiDispatch] = useReducer(uiReducer, {
    isLoading: false,
    showSidebar: true,
    modals: {
      isVisible: false,
      isCookieVisible: false
    },
    mobile: {
      subtitleHeader: ''
    }
  });

  const nameFinal = useMemo(() => {
    const user = userState.user;
    if (!user) return '';

    // Função auxiliar para limitar o nome a 3 palavras
    const limitWords = (name: string): string => {
      const words = name.trim().split(/\s+/);
      if (words.length <= 3) return name;
      return `${words.slice(0, 3).join(' ')}...`;
    };

    // Prioriza o nome alternativo, se existir
    if (user.alternative_name) {
      return limitWords(user.alternative_name);
    }

    // Usa FirstName e LastName se ambos existirem
    if (user.FirstName && user.LastName) {
      return limitWords(`${user.FirstName} ${user.LastName}`);
    }

    // Usa o Name como fallback
    return limitWords(user.Name || '');
  }, [userState.user]);

  const selectContent = useCallback(async (index: number) => {
    // if (index === contentState.contentSelected) return;

    uiDispatch({ type: 'SET_LOADING', payload: true });

    try {
      contentDispatch({ type: 'SET_CONTENT_SELECTED', payload: index });

      const currentContent = contentState.contents?.[index];
      if (!currentContent) return;

      const [_, newContent] = await Promise.all([
        setContentAction(index),
        getServerContent({
          ProposalId: currentContent.ProposalId || null,
          ContractId: currentContent.ContractId || null,
          type: currentContent.type,
          AccountId: currentContent.AccountId,
          EmpreendimentoId: currentContent.Empreendimento?.EmpreendimentoId,
          selectedIndex: index
        })
      ]);

      if (newContent && userState.user?.CodigoSienge__c && newContent.type === 'morador') {
        const statusBoletoAto = await checkStatusBoletoAto(
          userState.user.CodigoSienge__c,
          newContent.UnidadeAtivoName
        );

        newContent.statusBoletoAto = statusBoletoAto;
      }

      contentDispatch({ type: 'SET_CONTENT', payload: newContent });

      router.refresh();
    } catch (error) {
      console.error('Erro ao selecionar conteúdo:', error);
    } finally {
      uiDispatch({ type: 'SET_LOADING', payload: false });
    }
  }, []);

  const setShowFotoCallback = useCallback(
    (value: number | null) => {
      contentDispatch({ type: 'SET_SHOW_FOTO', payload: value });
    },
    [contentDispatch]
  );

  const setShowVideosCallback = useCallback(
    (value: number | null) => {
      contentDispatch({ type: 'SET_SHOW_VIDEOS', payload: value });
    },
    [contentDispatch]
  );

  const setImagesToSliderCallback = useCallback(
    (value: string[] | null) => {
      contentDispatch({ type: 'SET_IMAGES_TO_SLIDER', payload: value });
    },
    [contentDispatch]
  );

  const setVideosToSliderCallback = useCallback(
    (value: string[] | null) => {
      contentDispatch({ type: 'SET_VIDEOS_TO_SLIDER', payload: value });
    },
    [contentDispatch]
  );

  const setUserCallback = useCallback(
    (user: User | null) => userDispatch({ type: 'SET_USER', payload: user }),
    [userDispatch]
  );

  const setContentCallback = useCallback(
    (content: Content | null) => contentDispatch({ type: 'SET_CONTENT', payload: content }),
    [contentDispatch]
  );

  const setContentsCallback = useCallback(
    (contents: Content[] | null) => contentDispatch({ type: 'SET_CONTENTS', payload: contents }),
    [contentDispatch]
  );

  const setLoadingCallback = useCallback(
    (loading: boolean) => uiDispatch({ type: 'SET_LOADING', payload: loading }),
    [uiDispatch]
  );

  const setShowSidebarCallback = useCallback(
    (show: boolean) => uiDispatch({ type: 'SET_UI_STATE', payload: { showSidebar: show } }),
    [uiDispatch]
  );

  const setModalVisibleCallback = useCallback(
    (visible: boolean) =>
      uiDispatch({ type: 'SET_MODAL_STATE', payload: { type: 'default', value: visible } }),
    [uiDispatch]
  );

  const setModalCookieVisibleCallback = useCallback(
    (visible: boolean) =>
      uiDispatch({ type: 'SET_MODAL_STATE', payload: { type: 'cookie', value: visible } }),
    [uiDispatch]
  );

  const setSubtitleHeaderMobileCallback = useCallback(
    (subtitle: string) => uiDispatch({ type: 'SET_SUBTITLE_HEADER_MOBILE', payload: subtitle }),
    [uiDispatch]
  );

  const loadMoreContents = useCallback(async () => {
    if (contentState.isLoadingMore || !contentState.hasMoreContent) return;

    contentDispatch({ type: 'SET_LOADING_MORE', payload: true });

    try {
      const nextPage = contentState.currentPage + 1;
      const { getUserAssets } = await import('../server/actions/user');
      const response = await getUserAssets(nextPage, 20);

      if (response.contents && response.contents.length > 0) {
        const hasMore = response.pagination.current_page < response.pagination.last_page;

        setTimeout(() => {
          contentDispatch({ type: 'APPEND_CONTENTS', payload: response.contents });

          contentDispatch({ type: 'SET_CURRENT_PAGE', payload: response.pagination.current_page });
          contentDispatch({ type: 'SET_HAS_MORE_CONTENT', payload: hasMore });

          contentDispatch({ type: 'SET_LOADING_MORE', payload: false });
        }, 0);
      } else {
        contentDispatch({ type: 'SET_HAS_MORE_CONTENT', payload: false });
        contentDispatch({ type: 'SET_LOADING_MORE', payload: false });
      }
    } catch (error) {
      console.error('Error loading more contents:', error);
      contentDispatch({ type: 'SET_LOADING_MORE', payload: false });
    }
  }, [contentState.currentPage, contentState.isLoadingMore, contentState.hasMoreContent]);

  const contextValue = useMemo(
    () => ({
      // Estados
      user: userState.user,
      avatar: userState.avatar,
      contents: contentState.contents,
      content: contentState.content,
      contentSelected: contentState.contentSelected,
      imagesToSlider: contentState.imagesToSlider,
      videosToSlider: contentState.videosToSlider,
      showFoto: contentState.showFoto,
      showVideos: contentState.showVideos,
      isLoading: uiState.isLoading,
      showSidebar: uiState.showSidebar,
      modals: uiState.modals,
      mobile: uiState.mobile,
      // Estados de paginação
      hasMoreContent: contentState.hasMoreContent,
      isLoadingMore: contentState.isLoadingMore,
      currentPage: contentState.currentPage,

      // Valores computados
      nameFinal,
      isMobile,

      // Actions
      selectContent,
      setUser: setUserCallback,
      setContent: setContentCallback,
      setContents: setContentsCallback,
      setShowFoto: setShowFotoCallback,
      setShowVideos: setShowVideosCallback,
      setImagesToSlider: setImagesToSliderCallback,
      setVideosToSlider: setVideosToSliderCallback,
      setLoading: setLoadingCallback,
      setShowSidebar: setShowSidebarCallback,
      setModalVisible: setModalVisibleCallback,
      setModalCookieVisible: setModalCookieVisibleCallback,
      setSubtitleHeaderMobile: setSubtitleHeaderMobileCallback,
      loadMoreContents
    }),
    [
      // User state
      userState.user,
      userState.avatar,

      // Content state
      contentState.contents,
      contentState.content,
      contentState.contentSelected,
      contentState.imagesToSlider,
      contentState.videosToSlider,
      contentState.showFoto,
      contentState.showVideos,
      contentState.hasMoreContent,
      contentState.isLoadingMore,
      contentState.currentPage,

      // UI state
      uiState.isLoading,
      uiState.showSidebar,
      uiState.modals.isVisible,
      uiState.modals.isCookieVisible,
      uiState.mobile.subtitleHeader,
      isMobile,
      // Valores computados
      nameFinal,

      // Actions
      selectContent,
      setUserCallback,
      setContentCallback,
      setContentsCallback,
      setShowFotoCallback,
      setShowVideosCallback,
      setImagesToSliderCallback,
      setVideosToSliderCallback,
      setLoadingCallback,
      setShowSidebarCallback,
      setModalVisibleCallback,
      setModalCookieVisibleCallback,
      setSubtitleHeaderMobileCallback,
      loadMoreContents
    ]
  );
  return <AppContext.Provider value={contextValue}>{children}</AppContext.Provider>;
}
