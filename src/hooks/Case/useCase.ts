/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';

import { Case, CaseCreateError, SendCaseProps } from '@/@types/cases';
import { Content } from '@/@types/content';
import { Caso } from '@/@types/installments';
import { Proposal } from '@/@types/proposals';
import { User } from '@/@types/user';
import { useAppContext } from '@/contexts/AppContext';
import { apiGet, apiPost } from '@/server/services/api';
import { BrowserUtils } from '@/utils/BrowserUtils';
import { useRouter } from 'next/navigation';
import { useModal } from '..';
import { useData } from '../useData';
import { useLoading } from '../useLoading';

export const useCase = () => {
  const { user } = useAppContext();
  const { setLoadingInfo } = useLoading();
  // const { getFetch } = useApi();
  const { toggleModal } = useModal();
  const { createData } = useData();
  const router = useRouter();
  // const [cases, setCases] = useState([]);
  // const [loading, setLoading] = useState(false);
  // const [error, setError] = useState(null);

  /**
   * A function that checks if there is a case today based on the AccountId provided.
   *
   * @param {string} AccountId - The ID of the account to check for cases.
   * @param {string} filter - The filter to apply when checking for cases.
   * @return {Promise<boolean>} A promise that resolves to a boolean indicating if there's a case today.
   */
  const checkCaseToday = async ({
    AccountId,
    filter = 'renegociacao'
  }: {
    AccountId: string;
    filter?: string;
  }) => {
    try {
      // const result = await getFetch<ExistsCaseTodayResponse>({
      //   method: 'GET',
      //   route: `case/existsCaseToday/${AccountId}/${filter}`
      // });
      const result = await apiGet(`case/existsCaseToday/${AccountId}/${filter}`, {});

      if (result) {
        createData<boolean>({
          id: 'existsCaseToday',
          value: result.data.existsCaseToday,
          isSetCookie: false
        });
        return result.data.existsCaseToday;
      }
      return false;
    } catch (error) {
      console.error('Error checking case today:', error);
      return false;
    }
  };

  /**
   * Function to get a case after creation
   *
   * @param {string} idCase - The ID of the case to retrieve
   * @returns {Promise<Case|null>} The case data or null
   */
  const getCaseAfterCreate = async (idCase: string, attempt = 1): Promise<Case | null> => {
    if (!idCase) return null;

    const MAX_ATTEMPTS = 8;
    const RETRY_CREATE_ATTEMPT = 4;
    const INITIAL_DELAY = 300;

    const sleep = (ms: number) =>
      new Promise<void>((resolve) => {
        setTimeout(resolve, ms);
      });

    const handleSuccess = (newcase: Case) => {
      BrowserUtils.handleBodyScroll(false);
      toggleModal({
        text: `Sua solicitação número ${newcase.CaseNumber} foi criada com sucesso!`,
        params: {
          callback: () => {
            router.push(`/${user?.AccountId}/fale-com-a-cury/acompanhar-solicitacoes`);
          }
        }
      });
      setLoadingInfo(false);
      return newcase;
    };

    const handleError = (error: unknown) => {
      console.error('Error getting case after create:', error);
      BrowserUtils.handleBodyScroll(false);
      setLoadingInfo(false);
      return null;
    };

    try {
      const result = await apiGet(`case/${idCase}`, {});

      if (result?.data) {
        return handleSuccess(result.data as unknown as Case);
      }

      if (attempt === RETRY_CREATE_ATTEMPT) {
        await createCaseIfFlowDontWork(idCase, 'Id');
      }

      if (attempt >= MAX_ATTEMPTS) {
        return handleError(new Error('Maximum attempts reached'));
      }

      await sleep(attempt * INITIAL_DELAY);
      return getCaseAfterCreate(idCase, attempt + 1);
    } catch (error) {
      if (attempt >= MAX_ATTEMPTS) {
        return handleError(error);
      }
      await sleep(attempt * INITIAL_DELAY);
      return getCaseAfterCreate(idCase, attempt + 1);
    }
  };

  /**
   * Function to send a case
   *
   * @param {SendCaseProps} caseData - The case data to send
   * @returns {Promise<boolean>} Whether the case was sent successfully
   */

  function buildCaseRequestBody(
    params: SendCaseProps,
    user: User,
    content: Content | Proposal | null | undefined
  ): Record<string, any> {
    return {
      type: params.type || '',
      SuppliedName: params.SuppliedName || '',
      SuppliedEmail: params.SuppliedEmail || '',
      SuppliedPhone: params.SuppliedPhone || '',
      ContactId: user?.PersonContactId || '',
      AccountId: user?.AccountId || '',
      AssetId: content?.AssetId || '',
      DataCompra__c: content?.Contract?.DataCompra__c || '',
      OwnerId: params.OwnerId || '',
      Origin: 'Portal do Cliente',
      Description: params.description || '',
      Empreendimento__c: params.EmpreendimentoId || content?.Empreendimento.EmpreendimentoId || '',
      TipoAtendimento__c: params.tipoAtendimento || '',
      Classificacao__c: params.classificacao || '',
      OwnerName: params.OwnerName || '',
      recordName: params.recordName || '',
      Assunto__c: params.assunto || '',
      file: params.file || '',

      ...(params.MotivoCancelamento && { MotivoCancelamento__c: params.MotivoCancelamento }),
      ...(params.OrigemSolicitacaoAreaPrivativa__c && {
        OrigemSolicitacaoAreaPrivativa__c: params.OrigemSolicitacaoAreaPrivativa__c
      }),
      ...(params.DataAgendamento__c && { DataAgendamento__c: params.DataAgendamento__c }),
      ...(params.DataAgendamentoFormatada__c && {
        DataAgendamentoFormatada__c: params.DataAgendamentoFormatada__c
      }),
      ...(params.ClienteEntregouDocumentoParaCartorio__c !== undefined && {
        ClienteEntregouDocumentoParaCartorio__c: params.ClienteEntregouDocumentoParaCartorio__c
          ? 'true'
          : 'false'
      }),
      ...(params.TipoReembolso__c && { TipoReembolso__c: params.TipoReembolso__c }),
      ...(params.DataVencOriginalBoleto__c && {
        DataVencOriginalBoleto__c: params.DataVencOriginalBoleto__c
      }),
      ...(params.DataVencPagarBoleto__c && {
        DataVencPagarBoleto__c: params.DataVencPagarBoleto__c
      }),
      ...(params.ValorCheque__c && { ValorCheque__c: params.ValorCheque__c }),
      ...(params.DataCheque__c && { DataCheque__c: params.DataCheque__c }),
      ...(params.MesReferencia__c && { MesReferencia__c: params.MesReferencia__c })
    };
  }

  function getCustomErrorMessage(type: string, assunto: string): string {
    switch (type) {
      case 'case/insertCaseAgendamento':
        return '<p>Você já possui um agendamento em andamento.</p><p> Veja o status dele em nossa página de solicitações.</p>';
      case 'case/insertCaseNegociaAntecipa':
        return '<p>Você já possui uma negociação em andamento.</p><p> Veja o status dele em nossa página de solicitações.</p>';
      case 'case':
        if (assunto === 'Distrato') {
          return '<p>Você já possui uma solicitação de distrato em andamento.</p><p> Veja o status dele em nossa página de solicitações.</p>';
        }
        if (assunto === 'Troca de Unidade') {
          return '<p>Você já possui uma solicitação de troca de Unidade em andamento.</p><p> Veja o status dele em nossa página de solicitações.</p>';
        }
        break;
    }
    return 'Ocorreu um erro ao enviar sua solicitação.';
  }

  const sendCase = async (params: SendCaseProps): Promise<boolean> => {
    const { user, content, file = null, type = 'case', textLoadingCase, assunto } = params;

    try {
      setLoadingInfo(true, textLoadingCase || 'Enviando...');
      BrowserUtils.handleBodyScroll(false);

      const requestBody = buildCaseRequestBody(params, user, content);

      const result = await apiPost(type, requestBody, {}, file);
      if (result) {
        if (result.data) {
          await getCaseAfterCreate(result.data.newcase.data.id);
        }
      }

      BrowserUtils.handleBodyScroll(true);
      setLoadingInfo(false);
      return true;
    } catch (error: unknown) {
      console.log('Erro no fetch:', error);

      // Verifica se o erro é do tipo CaseCreateError
      const caseError = error as CaseCreateError;

      const errorMessage =
        caseError?.errors?.[0]?.message ||
        (error as Error)?.message ||
        'Erro desconhecido ao processar a requisição';

      const message =
        type === 'case/insertCaseAgendamento'
          ? errorMessage
          : getCustomErrorMessage(type, assunto) + `<br><br>Detalhes: ${errorMessage}`;

      BrowserUtils.handleBodyScroll(true);
      setLoadingInfo(false);
      /*
      toggleModal({
        text: message,
        params: {
          callback: () => {
            router.push(`/${user?.AccountId}/fale-com-a-cury/acompanhar-solicitacoes`);
          }
        }
      });
*/
      return false;
    }
  };

  /**
   * Function to get cases
   *
   * @param {string} filter - The filter to apply
   * @returns {Case[]} Array of cases
   */

  // const GetCases = (accountId: string, filter: string = '') => {
  // const [cases, setCases] = useState<Case[]>([]);
  // const [error, setError] = useState<Error | null>(null);
  // const fetchCases = useCallback(async () => {
  //   try {
  //     const result = await getFetch<CasesResponse>({
  //       method: 'GET',
  //       route: `case/showCases/${accountId}/${filter}`
  //     });
  //     if (result && result.data && result.data.cases) {
  //       setCases(result.data.cases as Case[]);
  //     }
  //   } catch (error) {
  //     console.error('Error fetching cases:', error);
  //   }
  // }, [filter]);
  // useEffect(() => {
  //   let isMounted = true;
  //   const fetchCases = async () => {
  //     try {
  //       const result = await getFetch<CasesResponse>({
  //         method: 'GET',
  //         route: `case/showCases/${accountId}/${filter}`
  //       });
  //       if (isMounted && result && result.data && result.data.cases) {
  //         setCases(result.data.cases as Case[]);
  //       }
  //     } catch (error) {
  //       console.error('Error fetching cases:', error);
  //       if (isMounted) {
  //         setError(error as Error);
  //         // setLoading(false);
  //       }
  //     }
  //   };
  //   fetchCases();
  //   return () => {
  //     isMounted = false;
  //   };
  //   // }, [fetchCases, filter]);
  // }, [filter]);
  // return cases;
  // };

  /**
   * Create a case if the flow doesn't work
   *
   * @param {string} dataId - The ID of the data
   * @param {string} type - The type of the data
   * @returns {Promise<any>} The result of the creation
   */
  const createCaseIfFlowDontWork = async (dataId: string, type: string) => {
    if (!dataId) return false;

    try {
      return await apiPost('case/createCaseIfFlowDontWork', {
        type,
        dataId
      });
      // return await getFetch<Case>({
      //   method: 'POST',
      //   route: 'case/createCaseIfFlowDontWork',
      //   body: {
      //     type,
      //     dataId
      //   }
      // });
    } catch (error) {
      // eslint-disable-next-line quotes
      console.error("Error creating case if flow doesn't work:", error);
      return false;
    }
  };

  /**
   * Create a case after API return
   *
   * @param {Caso} data - The case data
   * @returns {Promise<any>} The result of the creation
   */
  const createCaseAfterApiReturn = async (data: Caso) => {
    if (!data) return false;

    try {
      return await apiPost('case/createCaseAfterApiReturn', {
        data
      });
      // return await getFetch<Case>({
      //   method: 'POST',
      //   route: 'case/createCaseAfterApiReturn',
      //   body: {
      // data
      //   }
      // });
    } catch (error) {
      console.error('Error creating case after API return:', error);
      return false;
    }
  };

  return {
    // GetCases,
    sendCase,
    checkCaseToday,
    createCaseIfFlowDontWork,
    createCaseAfterApiReturn
  };
};
