/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use client';
import { Indexer, Installments } from '@/@types/installments';
import { IndexerResponse, IrResponse } from '@/@types/responses';
import { apiGet } from '@/server/services/api';
import { useCallback, useState } from 'react';
import { useModal } from '../../contexts/useModal';
import { useApi } from '../useApi';

const URL_SERVICE_SIENGE = 'services/sienge/';

export const useInstallments = () => {
  const { getFetch } = useApi();
  const { toggleModal } = useModal();
  const [indexerName, setIndexerName] = useState<string>('');
  const [allIndexer, setAllIndexer] = useState<Indexer[]>([]);
  const [installments, setInstallments] = useState<Installments | null>(null);
  const [boletoGerado, setBoletoGerado] = useState<string>('');
  const [urlBoleto, setUrlBoleto] = useState<string>('');
  const [digitableNumberBoleto, setDigitableNumberBoleto] = useState<string>('');

  const getIndexerName = useCallback(async (indexerId: number) => {
    try {
      // const response = await getFetch<IndexerResponse>({
      //   method: 'GET',
      //   route: 'indexer/all'
      // });
      const response = await apiGet<IndexerResponse>('indexer/all');

      if (response?.data.indexers) {
        setAllIndexer(response.data.indexers);
        setIndexerName(response.data.indexers[indexerId].name);
        return response.data.indexers[indexerId].name;
      }
      return '';
    } catch (err) {
      console.error(err);
      return '';
    }
  }, []);

  const getBoleto = useCallback(
    async (installmentId: number, billReceivableId: number, UnidadeAtivoName: string) => {
      try {
        setBoletoGerado('');
        setUrlBoleto('');
        setDigitableNumberBoleto('');

        const response = await getFetch<{
          results: {
            urlReport: string;
            nameFile: string;
            digitableNumber: string;
          }[];
        }>({
          method: 'POST',
          route: URL_SERVICE_SIENGE,
          body: {
            service: 'boleto',
            installmentId,
            billReceivableId,
            UnidadeAtivoName
          }
        });
        const data = await Promise.resolve(response);
        if (!data) {
          toggleModal({ text: 'Não foi possível pegar seu boleto.' });
        } else {
          setBoletoGerado('ok');
          setUrlBoleto(data.results[0].urlReport);
          setDigitableNumberBoleto(data.results[0].digitableNumber);
        }
      } catch (error) {
        console.error(error);
      }
    },
    []
  );

  const getIr = useCallback(
    async ({
      customerId,
      companyId,
      year,
      filename,
      sendEmail = false
    }: {
      customerId: string;
      companyId: string;
      year: string;
      filename: string;
      sendEmail: boolean;
    }) => {
      try {
        const response = await getFetch<{ results: { urlReport: string }[] } | boolean>({
          method: 'POST',
          route: URL_SERVICE_SIENGE,
          body: {
            service: 'ir',
            revalidate: 3600,
            customerId,
            companyId,
            year,
            sendEmail
          }
        });
        if (response === false) {
          return false;
        }

        const data = (await Promise.resolve(response)) as { results: { urlReport: string }[] };

        if (sendEmail) {
          return await getFetch<IrResponse>({
            method: 'POST',
            route: 'ir',
            body: {
              customerId,
              companyId,
              year,
              sendEmail,
              filename,
              irPDF: data?.results[0].urlReport
            }
          });
        }

        return {
          irPDF: data?.results[0].urlReport
        };
      } catch (error: unknown) {
        return error as Error;
      }
    },
    []
  );

  const getInstallments = useCallback(
    async (CodigoSienge__c: string, UnidadeAtivoName: string): Promise<Installments | Error> => {
      try {
        localStorage.setItem('CodigoSienge__c', CodigoSienge__c);
        localStorage.setItem('UnidadeAtivoName', UnidadeAtivoName);
        const response = await getFetch<Installments>({
          method: 'POST',
          route: URL_SERVICE_SIENGE,
          body: {
            service: 'installment',
            revalidate: 0,
            CodigoSienge__c,
            UnidadeAtivoName
          },
          isNegotiate: true
        });
        const data = await Promise.resolve(response);
        setInstallments(data);
        return data as Installments;
      } catch (error) {
        console.error(error);
        return error as Error;
      }
    },
    []
  );

  const checkBoletoAto = useCallback(
    async (CodigoSienge__c: string, UnidadeAtivoName: string): Promise<string | Error> => {
      try {
        localStorage.setItem('CodigoSienge__c', CodigoSienge__c);
        localStorage.setItem('UnidadeAtivoName', UnidadeAtivoName);

        const response = await getFetch<{ data: { boletoAto: string } }>({
          method: 'POST',
          route: 'checkBoletoAto',
          body: {
            CodigoSienge__c,
            UnidadeAtivoName
          },
          isNegotiate: true
        });
        const data = await Promise.resolve(response);
        return data?.data.boletoAto as string;
      } catch (error) {
        console.error(error);
        return error as Error;
      }
    },
    []
  );
  return {
    getIndexerName,
    indexerName,
    getBoleto,
    getIr,
    getInstallments,
    installments,
    boletoGerado,
    setBoletoGerado,
    urlBoleto,
    setUrlBoleto,
    digitableNumberBoleto,
    setDigitableNumberBoleto,
    allIndexer,
    checkBoletoAto
  };
};
