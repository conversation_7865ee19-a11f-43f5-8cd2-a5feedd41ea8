/*
 * Aplicativo de Gerenciamento de Empreendimentos para Clientes Cury
 * Copyright (C) 2024 StudioWox
 *
 * Desenvolvido exclusivamente para Cury por StudioWox.
 *
 * Este código é licenciado exclusivamente para a empresa Cury e não pode ser redistribuído ou utilizado
 * por terceiros sem permissão expressa por escrito de StudioWox e Cury.
 *
 * Para mais informações, entre em contato com:
 * Thalles Freitas - <EMAIL>
 * StudioWox: https://studiowox.com.br
 * Cury: https://cury.net
 *
 * Todos os direitos reservados.
 */

'use server';
import { AppProvider } from '@/contexts/AppContext';
import { fetchInitialServerData } from '@/server/actions/content';
import { getServerIsImpersonate } from '@/server/services/ImpersonationService';
import { Show } from '@/utils/components/Show';
import dynamic from 'next/dynamic';
import RefreshPage from '@/templates/components/Commons/RefreshPage';
import { JSX, ReactNode } from 'react';
import ClientProviders from './ClientProviders';
import ClientTemplate from './clientTemplate';

import {
  CustomLoading,
  ImpersonationBanner,
  IrScreen,
  Modal,
  ModalCookies,
  ModalFirstLogin,
  ModalPhotosEnterprise,
  ModalRelationshipCenter,
  ModalVideosEnterprise,
  ServiceWorkerRegistration
} from '@/utils/lazyImports';

// const ServiceWorkerRegistration = dynamic(
//   () => import('@/templates/components/Commons/ServiceWorkerRegistration'),
//   {
//     loading: () => <></>
//   }
// );

// const RefreshPage = dynamic(() => import('@/templates/components/Commons/RefreshPage'), {
//   loading: () => <></>
// });

// const ModalFirstLogin = dynamic(() => import('@/templates/components/Commons/ModalFirstLogin'), {
//   loading: () => <></>
// });

// const ModalPhotosEnterprise = dynamic(
//   () => import('@/templates/WorkInProgress/components/ModalPhotosEnterprise'),
//   {
//     loading: () => <></>
//   }
// );

// const ModalVideosEnterprise = dynamic(
//   () => import('@/templates/WorkInProgress/components/ModalVideosEnterprise'),
//   {
//     loading: () => <></>
//   }
// );

// const ModalRelationshipCenter = dynamic(
//   () => import('@/templates/TalkToCury/components/ModalRelationshipCenter'),
//   {
//     loading: () => <></>
//   }
// );

// const ModalCookies = dynamic(() => import('@/templates/TalkToCury/components/ModalCookies'), {
//   loading: () => <></>
// });

// const IrScreen = dynamic(
//   () =>
//     import(
//       '@/templates/Financial/NegotiateYourInstallmentsScreen/components/NegotiatedConditions/IrScreen'
//     ),
//   {
//     loading: () => <></>
//   }
// );

// const Modal = dynamic(() => import('@/templates/components/Commons/Modal'), {
//   loading: () => <></>
// });

// const CustomLoading = dynamic(
//   () => import('@/templates/components/Commons/Loading/CustomLoading'),
//   {
//     loading: () => <></>
//   }
// );

// const ImpersonationBanner = dynamic(
//   () => import('@/templates/components/Commons/Impersonation/ImpersonationBanner'),
//   {
//     loading: () => <></>
//   }
// );

/**
 * Template principal da aplicação. Este componente é renderizado no servidor
 * e fornece dados iniciais para o ClientTemplate.
 */

interface TemplateProps {
  children: ReactNode;
}

export default async function Template({ children }: TemplateProps): Promise<JSX.Element> {
  const serverData = await fetchInitialServerData();
  const {
    initialUser,
    initialContentData: initialContent,
    initialContentsData: initialContents,
    initialContentSelected,
    environment
  } = serverData;

  const hasImpersonateToken = await getServerIsImpersonate();
  return (
    <ClientProviders>
      <AppProvider
        initialUser={initialUser}
        initialContents={initialContents || null}
        initialContentSelected={initialContentSelected || 0}
        initialContent={initialContent || null}
        environment={environment}>
        <ServiceWorkerRegistration />
        <RefreshPage />
        <ModalFirstLogin />
        <ModalVideosEnterprise />
        <ModalPhotosEnterprise />
        <ModalRelationshipCenter />
        <ModalCookies />
        <IrScreen />
        <Modal />
        <CustomLoading />
        <Show when={hasImpersonateToken}>
          <ImpersonationBanner />
        </Show>
        <ClientTemplate>{children}</ClientTemplate>
      </AppProvider>
    </ClientProviders>
  );
}
