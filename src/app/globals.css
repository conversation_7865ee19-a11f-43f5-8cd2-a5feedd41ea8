@import url('https://fonts.googleapis.com/css2?family=Ubuntu:wght@300;400;500;700&display=swap');
/*  */

@import 'tailwindcss';
/*
@tailwind base;
@tailwind components;
@tailwind utilities; */

html {
  touch-action: manipulation;
}

@theme {
  /* Breakpoints (screens) */
  --breakpoint-xs: 325px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Fontes personalizadas */
  --font-family-ubuntu: 'Ubuntu', sans-serif;

  /* Gradientes personalizados */
  --background-image-gradient-radial: radial-gradient(var(--tw-gradient-stops));
  --background-image-gradient-conic: conic-gradient(from 180deg at 50% 50%,
      var(--tw-gradient-stops));

  /* Cores personalizadas */
  --color-navy-blue: #1a374d;
  --color-salmon: #ff686b;
  --color-light-gray: #e4e7ea;
  --color-neutral-blue: #62829a;
  --color-blue-secondary: #3e92cc;
  --color-light-blue: #2d719f;
  --color-blue-tertiary: #79bedf;

  /* Larguras personalizadas */
  --width-256: 256px;
  --width-86: 86%;
  --width-39: 39%;
  --width-18: 74px;

  /* Alturas personalizadas */
  --height-18: 74px;
  --height-13: 52px;

  /* Espaçamentos personalizados */
  --spacing-90: 90px;
  --spacing-70: 70px;
  --spacing-17: 17px;
  --spacing-62: 247px;

  /* Espessura de bordas personalizadas */
  --border-width-1: 1px;
  --border-width-15: 15px;

  /* Tamanhos de fonte personalizados */
  --font-size-10: 10px;
  --font-size-9: 9px;

  /* Rotação de matiz personalizada */
  --hue-rotate-210: 210deg;

  /* Sombras personalizadas */
  --drop-shadow-blue: 0px 1px 5px rgba(98, 130, 154, 1);
  --box-shadow-white: 0px 1px 5px 0px rgba(228, 231, 234, 1);

  /* Espaçamento entre letras personalizado */
  --letter-spacing-1: 1px;
}

@font-face {
  font-family: 'Salesforce Sans';
  font-display: swap;
  src:
    url('https://c1.sfdcstatic.com/etc/clientlibs/sfdc-aem-master/clientlibs_base/fonts/SalesforceSans-Regular.woff') format('woff'),
    url('https://c1.sfdcstatic.com/etc/clientlibs/sfdc-aem-master/clientlibs_base/fonts/SalesforceSans-Regular.ttf') format('truetype');
}

@font-face {
  font-family: 'Salesforce Sans';
  font-display: swap;
  src:
    url('https://c1.sfdcstatic.com/etc/clientlibs/sfdc-aem-master/clientlibs_base/fonts/SalesforceSans-Regular.woff') format('woff'),
    url('https://c1.sfdcstatic.com/etc/clientlibs/sfdc-aem-master/clientlibs_base/fonts/SalesforceSans-Regular.ttf') format('truetype');
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: #e5e7ea;
  touch-action: pan-x pan-y;
  -ms-touch-action: pan-x pan-y;
}

.container {
  width: 100%;
}

@media (min-width: 325px) {
  .container {
    max-width: 100%;
  }
}

@media (min-width: 640px) {
  .container {
    max-width: 100%;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.no-scrollbar::-webkit-scrollbar {
  display: none;
}

.no-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.slick-next::before,
.slick-prev::before {
  display: none;
}

.slick-prev img {
  transform: rotate(90deg) !important;
}

.slick-next img {
  transform: rotate(-90deg) !important;
}

.modalpdf .react-pdf__Page {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* .react-pdf__Document,
.react-pdf__Page,
.react-pdf__Page__annotations.annotationLayer,
.react-pdf__Page__textContent.textLayer,
.react-pdf__Page__canvas{
  width: auto !important;
  height: 100% !important ;
  max-width: 100% !important;
  max-height: auto !important;
} */

@media screen and (orientation: landscape) {

  .react-pdf__Document,
  .react-pdf__Page,
  .react-pdf__Page__annotations.annotationLayer,
  .react-pdf__Page__textContent.textLayer,
  .react-pdf__Page__textContent .textLayer,
  .react-pdf__Page__canvas {
    width: auto !important;
    height: 100% !important;
    max-width: 100% !important;
    max-height: auto !important;
  }
}

.react-pdf__Page__textContent.textLayer,
.react-pdf__Page__textContent .textLayer {
  height: 1% !important;
}

@media screen and (orientation: portrait) {

  .react-pdf__Document,
  .react-pdf__Page,
  .react-pdf__Page__annotations.annotationLayer,
  .react-pdf__Page__textContent.textLayer,
  .react-pdf__Page__canvas {
    width: 100% !important;
    height: auto !important;
    max-width: none !important;
    max-height: none !important;
  }
}

.swiper-button-next {
  position: absolute;
  top: 60%;
  right: -50px;
  transform: translateY(-30%);
  z-index: 10;
}

.swiper-button-prev {
  position: absolute;
  top: 60%;
  left: -25px;
  transform: translateY(-30%);
  z-index: 10;
}

.swiper-button-prev:after,
.swiper-button-next:after {
  color: rgb(26 55 77 / var(--tw-text-opacity));
  font-size: 17px !important;
}

/* Esconde as setinhas do input type number */
input[type='number']::-webkit-outer-spin-button,
input[type='number']::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type='number'] {
  -moz-appearance: textfield;
  /* Firefox */
}

.limited-lines {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: normal;
  max-height: 3em;
  line-height: 1.5em;
  width: 80%;
}

.embeddedMessagingConversationButton {
  position: relative;
  bottom: 130px !important;
  right: 14px !important;
}

.embeddedMessagingFrame {
  bottom: 130px !important;
}

@media screen and (max-width: 450px) {
  .embeddedMessagingFrame {
    bottom: 100px !important;
    max-height: calc(50vh);
    z-index: 2;
  }

  .embedded-messaging>.embeddedMessagingFrame[class~='isMaximized'] {
    max-height: calc(100vh - 16em) !important;
  }

  .embeddedMessagingPreventScrolling .blockheader {
    display: block;
    width: 100%;
    height: 100px;
    position: absolute;
    z-index: 12312313121312132;
    top: 100px;
    display: flex;
    justify-content: space-between;
  }

  .embeddedMessagingPreventScrolling .blockheader .esq {
    width: 26px;
    height: 40px;
    background: #12374d;
    top: 44px;
    display: block;
    position: relative;
    left: 30px;
  }

  .embeddedMessagingPreventScrolling .blockheader .dir {
    width: 26px;
    height: 40px;
    background: #12374d;
    top: 44px;
    display: block;
    position: relative;
    right: 30px;
  }
}

.embeddedServiceHelpButton .helpButton .uiButton {
  background-color: #57b4e5;
  font-family: 'Salesforce Sans', sans-serif;
}

.embeddedServiceHelpButton .helpButton .uiButton:focus {
  outline: 1px solid #57b4e5;
}

@media screen and (max-width: 768px) {
  .helpButton {
    right: initial;
    bottom: initial;
    left: 50%;
    margin-left: -84px !important;
    top: 50%;
    margin-top: -23px !important;
  }

  .minimizeButton,
  .closeButton {
    display: none;
  }

  .dockableContainer.showDockableContainer.embeddedServiceBottomTabBar {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  .embeddedServiceSidebar.layout-docked .embeddedServiceBottomTabBar.dockableContainer.showDockableContainer {
    height: 100%;
  }

  .embeddedServiceSidebar.layout-docked .dockableContainer {
    max-width: 100%;
  }

  #embedded-messaging {
    display: none;
  }
}

#embeddedMessagingModalOverlay {
  background: none !important;
  /*background-color:none !important;*/
}

.blockheader {
  display: none;
}

.react-pdf__Page__textContent,
.react-pdf__Page__annotations {
  display: none;
}