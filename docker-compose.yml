services:
  nextjs:
    container_name: nextjs-app-master-homolog
    build:
      context: .
      dockerfile: Dockerfile
      target: base
    ports:
      - '3000:3000'
    volumes:
      - ./:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NODE_ENV=development
      - WATCHPACK_POLLING=true
    env_file:
      - .env.local
    command: pnpm run dev-server-turbo
    restart: unless-stopped
    networks:
      - cury_app_cliente_backend_cury-cliente

networks:
  cury_app_cliente_backend_cury-cliente:
    external: true
