import { fixupConfigRules, fixupPluginRules } from '@eslint/compat';
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import typescriptEslint from '@typescript-eslint/eslint-plugin';
import react from 'eslint-plugin-react';
import { defineConfig } from 'eslint/config';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all
});

export default defineConfig([
  {
    // extends: compat.extends('next'),

    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module'
    },

    // eslint-disable-next-line no-undef
    extends: fixupConfigRules(
      compat.extends(
        'next/core-web-vitals',
        'eslint:recommended',
        'plugin:@typescript-eslint/recommended',
        'plugin:react/recommended',
        'plugin:react-hooks/recommended'
      )
    ),

    plugins: {
      '@typescript-eslint': fixupPluginRules(typescriptEslint),
      'react': fixupPluginRules(react)
    },

    ignores: [
      'node_modules/',
      '.next/',
      'dist/',
      'build/',
      'coverage/',
      '*.config.js',
      '*.config.ts',
      'public/',
      '.vercel/',
      '*.d.ts',
      '*.test.*',
      '*.spec.*'
    ],

    rules: {
      'semi': ['error', 'always'],
      'quotes': ['error', 'single'],
      'indent': ['off'],
      'react-hooks/exhaustive-deps': 'off',
      'no-unused-vars': 'off',
      'no-console': 'off',
      'react/react-in-jsx-scope': 'off',
      'react/prop-types': 'off',

      // Regras críticas de segurança e qualidade
      'no-eval': 'error', // Previne uso de eval() que pode executar código malicioso
      'no-implied-eval': 'error', // Previne uso indireto de eval
      'no-new-func': 'error', // Previne criação de funções usando new Function()
      'no-prototype-builtins': 'error', // Previne chamadas diretas de métodos Object.prototype
      'no-return-await': 'off', // Previne return await desnecessários que impactam performance
      'no-script-url': 'error', // Previne uso de javascript: URLs que podem ser inseguras
      'no-var': 'error', // Força uso de let/const ao invés de var
      'prefer-const': 'error', // Força uso de const quando variável não é reatribuída
      'eqeqeq': ['error', 'always'], // Força uso de === ao invés de ==

      // Regras React importantes
      'react/no-danger': 'error', // Previne uso de dangerouslySetInnerHTML
      'react/jsx-key': 'error', // Força uso de key em elementos de lista
      'react/no-children-prop': 'error', // Previne passagem incorreta de children
      'react/no-direct-mutation-state': 'error', // Previne mutação direta do state

      // Regras de promises e async
      'no-async-promise-executor': 'error', // Previne executores de Promise async
      'no-await-in-loop': 'warn', // Alerta sobre await em loops que poderiam ser paralelos
      'no-promise-executor-return': 'error', // Previne retornos em executores de Promise

      // Regras de TypeScript (se estiver usando)
      '@typescript-eslint/no-explicit-any': 'warn', // Previne uso de any
      '@typescript-eslint/explicit-function-return-type': 'warn', // Força tipos de retorno explícitos
      '@typescript-eslint/no-non-null-assertion': 'warn', // Previne uso do operador !
      '@typescript-eslint/no-unused-vars': 'warn' // Previne uso de variáveis não utilizadas
    }
  }
]);
