import { withSentryConfig } from '@sentry/nextjs';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
const __dirname = path.dirname(fileURLToPath(import.meta.url));
const __filename = fileURLToPath(import.meta.url);

// Set Sentry Turbopack warning suppression
process.env.SENTRY_SUPPRESS_TURBOPACK_WARNING = '1';

// Protection for process.env
try {
  if (process.env && Object.getOwnPropertyDescriptor(process, 'env')) {
    const originalEnvDescriptor = Object.getOwnPropertyDescriptor(process, 'env');
    if (originalEnvDescriptor && originalEnvDescriptor.configurable) {
      const envCopy = { ...process.env };
      Object.defineProperty(process, 'env', {
        get() {
          return envCopy;
        },
        set() {
          return true;
        },
        configurable: true,
        enumerable: true
      });
    }
  }
} catch (e) {
  console.warn('Failed to protect process.env:', e);
}

/**
 * @type {import('next').NextConfig}
 */
const nextConfig = {
  output: 'standalone',
  env: {
    SENTRY_SUPPRESS_TURBOPACK_WARNING: '1',
    GIT_HASH: process.env.GIT_HASH || ''
  },
  
  async redirects() {
    return [
      {
        source: '/android',
        destination: 'https://play.google.com/store/apps/details?id=net.cury.cliente',
        permanent: true
      },
      {
        source: '/apple',
        destination: 'https://apps.apple.com/app/cury-cliente-app/id6504584275',
        permanent: true
      },
      {
        source: '/contato',
        destination:
          'https://wa.me/551131171300?text=Ol%C3%A1%21+Obrigado+por+entrar+em+contato+com+a+Cury+Construtora.+Como+podemos+ajudar+voc%C3%AA+hoje%3F',
        permanent: true
      }
    ];
  },

  async headers() {
    return [
      {
        source: '/service-worker.js',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0'
          }
        ]
      },
      {
        source: '/service-worker.js:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, max-age=0'
          }
        ]
      }
    ];
  },
  
  staticPageGenerationTimeout: 120,

  webpack: (config, { dev, isServer }) => {
    if (dev) {
      if (!isServer) {
        config.module = config.module || {};
        config.module.noParse = config.module.noParse || [];

        if (!Array.isArray(config.module.noParse)) {
          config.module.noParse = [config.module.noParse];
        }

        config.module.noParse.push(/@pnpm\/npm-conf/);
      }
    } else {
      config.cache = {
        type: 'filesystem',
        buildDependencies: {
          config: [__filename]
        },
        cacheDirectory: path.resolve(__dirname, '.next/cache/webpack')
      };
      
      config.optimization.usedExports = true;
      config.optimization.sideEffects = true;
      config.optimization.minimize = true;
      config.optimization.concatenateModules = true;
      config.optimization.splitChunks = {
        chunks: 'all',
        maxInitialRequests: Infinity,
        minSize: 0,
        cacheGroups: {
          framerMotion: {
            test: /[\\/]node_modules[\\/](framer-motion)[\\/]/,
            name: 'framer-motion-chunk',
            priority: 10,
            chunks: 'all'
          },
          swiper: {
            test: /[\\/]node_modules[\\/](swiper)[\\/]/,
            name: 'swiper-chunk',
            priority: 10,
            chunks: 'all'
          },
          monaInputMask: {
            test: /[\\/]node_modules[\\/](@mona-health[\\/]react-input-mask)[\\/]/,
            name: 'input-mask-chunk',
            priority: 10,
            chunks: 'all'
          },
          nookies: {
            test: /[\\/]node_modules[\\/](nookies|cookie|set-cookie-parser)[\\/]/,
            name: 'cookies-chunk',
            priority: 10,
            chunks: 'all'
          },
          propTypes: {
            test: /[\\/]node_modules[\\/](prop-types|invariant|warning)[\\/]/,
            name: 'prop-types-chunk',
            priority: 10,
            chunks: 'all'
          }
        }
      };
    }

    config.resolve.alias.canvas = false;
    config.resolve.alias['@'] = path.resolve(__dirname, 'src/');
    config.resolve.alias['@pnpm/npm-conf'] = path.resolve(__dirname, 'src/config/env.ts');

    return config;
  },
  
  reactStrictMode: false,
  productionBrowserSourceMaps: true,
  
  images: {
    unoptimized: false,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'curyconstrutora--uatfull.sandbox.my.salesforce.com',
        pathname: '/**'
      },
      {
        protocol: 'https',
        hostname: 's3clienteappcury.s3.amazonaws.com',
        pathname: '/**'
      },
      {
        protocol: 'https',
        hostname: 'drive.google.com'
      },
      {
        protocol: 'https',
        hostname: 'drive.usercontent.google.com'
      },
      {
        protocol: 'https',
        hostname: 'curyconstrutora.my.site.com'
      }
    ]
  },

  compiler: {
    // removeConsole: process.env.NODE_ENV === 'production'
  },
  
  // CONFIGURAÇÕES CRÍTICAS PARA FRAMER MOTION
  transpilePackages: ['framer-motion'],
  
  bundlePagesRouterDependencies: true,
  
  turbopack: {
    resolveAlias: {
      '@': path.resolve(__dirname, 'src/'),
      '@pnpm/npm-conf': path.resolve(__dirname, 'src/config/env.ts')
    },
    resolveExtensions: ['.mdx', '.tsx', '.ts', '.jsx', '.js', '.mjs', '.json']
  },
  
  experimental: {
    webpackMemoryOptimizations: true,
    webpackBuildWorker: true,
    optimizeCss: true,
    // ADICIONADO: Permite export * em client boundaries
    clientTraceMetadata: true,
    // ADICIONADO: Melhora compatibilidade com ESM
    esmExternals: 'loose',
    // ADICIONADO: Otimiza imports de pacotes
    optimizePackageImports: ['framer-motion']
  },
  
  eslint: {
    ignoreDuringBuilds: false,
    dirs: ['pages', 'components', 'lib', 'utils', 'hooks', 'src']
  },
  
  generateBuildId: async () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hour = String(now.getHours()).padStart(2, '0');
    const minute = String(now.getMinutes()).padStart(2, '0');

    const buildId = `build-${year}${month}${day}-${hour}${minute}`;

    const filePath = path.join(__dirname, 'src', 'BuildId.tsx');
    const content = `// Este arquivo é gerado automaticamente
export const BUILD_ID = '${buildId}';`;

    fs.writeFileSync(filePath, content);
    return buildId;
  }
};

const nextConfigWithSentry = withSentryConfig(nextConfig, {
  org: 'studio-wox-01',
  project: 'curycliente_front',
  silent: !process.env.CI,
  widenClientFileUpload: true,
  hideSourceMaps: true,
  disableLogger: true,
  automaticVercelMonitors: true,
  sourcemaps: {
    deleteSourcemapsAfterUpload: true
  }
});

export default nextConfigWithSentry;