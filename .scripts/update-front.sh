# git fetch && git checkout master
# git reset --hard @{u}
# sudo cp /var/www/html/front/.scripts/update-front.sh /usr/local/bin/update-front
# sudo chmod +x /usr/local/bin/update-front
# update-front

#!/bin/bash

# Mover o script de atualização para /usr/local/bin
# sudo cp /var/www/html/front/.scripts/update-front.sh /usr/local/bin/update-front
# sudo chmod +x /usr/local/bin/update-front
# update-front - para rodar o script

# Definir variáveis
MAIN_DIR="/var/www/html"
FRONT_DIR="$MAIN_DIR/front"
BACKUP_DIR="$MAIN_DIR/front_backups"
DATE=$(date +%Y%m%d_%H%M%S)
TEMP_DIR="$BACKUP_DIR/temp_$DATE"
CURRENT_BACKUP="$BACKUP_DIR/front_backup_$DATE"

# Criar diretório de backups se não existir
mkdir -p "$BACKUP_DIR"

# Criar backup da pasta front atual
echo "Criando backup da pasta front..."
cp -r "$FRONT_DIR" "$CURRENT_BACKUP"
echo "Backup criado com sucesso."

# Criar pasta temporária para atualização
mkdir -p "$TEMP_DIR"
cp -r "$FRONT_DIR/." "$TEMP_DIR/"

# Entrar na pasta temporária e atualizar o repositório
echo "Entrando na pasta temporária e atualizando o repositório..."
cd "$TEMP_DIR"

# Atualizar o repositório
git fetch
git reset --hard @{u}

# Limpar e reconstruir o projeto
echo "Limpando e reconstruindo o projeto..."
rm -rf node_modules .next
npm install

# Tentar fazer o build
echo "Executando o build..."
if npm run build; then
    echo "Build concluído com sucesso."

    # Parar o serviço atual
    echo "Parando o serviço atual..."
    sudo fuser -k 3000/tcp
    pm2 delete all

    # Mover a pasta temporária para substituir a pasta front
    echo "Substituindo a pasta front pela versão atualizada..."
    rm -rf "$FRONT_DIR"
    mv "$TEMP_DIR" "$FRONT_DIR"

    # Iniciar o serviço com a nova versão
    echo "Iniciando o serviço com a nova versão..."
    cd "$FRONT_DIR"
    pm2 start .scripts/ecosystem.config.cjs

    # Apagar todos os backups anteriores, mantendo apenas o atual
    echo "Apagando backups anteriores..."
    find "$BACKUP_DIR" -type d -name "front_backup_*" ! -name "front_backup_$DATE" -exec rm -rf {} \;

    echo "Atualização concluída com sucesso!"
else
    echo "Erro no build. Mantendo a versão atual e preservando o backup."
    # Não precisa fazer nada com a pasta front original, já que não deu certo
fi

# Limpar pastas temporárias
echo "Limpando pastas temporárias..."
find "$BACKUP_DIR" -type d -name "temp_*" -exec rm -rf {} \;

echo "Processo finalizado."