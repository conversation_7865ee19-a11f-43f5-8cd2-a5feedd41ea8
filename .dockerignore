# Arquivos de node
node_modules
npm-debug.log
yarn-debug.log
yarn-error.log

# Build e cache
.next
.turbo
out
dist
build
.vercel
.cache

# Arquivos de desenvolvimento
.git
.github
.vscode
.idea
*.md
*.log
*.lock
.eslintcache
tsconfig.tsbuildinfo

# Logs e backups
logs
*.log.*
*~
*.bak

# Arquivos de teste
__tests__
test
tests
coverage
.nyc_output

# Arquivos de ambiente (apenas incluir os específicos de produção se necessário)
.env
.env.*
!.env.production

# Arquivos Docker
Dockerfile*
docker-compose*
.dockerignore

# Outros
.DS_Store
thumbs.db
.editorconfig
.prettierrc
.eslintrc*
*.orig